package com.example.finalapp.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.*
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import com.example.finalapp.util.Constants.ButtonLabels
import com.example.finalapp.util.Constants.FieldLabels
import com.example.finalapp.util.ValidationUtils

/**
 * Reusable confirmation dialog component
 */
@Composable
fun ConfirmationDialog(
    title: String,
    message: String,
    confirmButtonText: String = ButtonLabels.YES,
    dismissButtonText: String = ButtonLabels.NO,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    isDestructive: Boolean = false
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = title,
                modifier = Modifier.semantics {
                    heading()
                }
            )
        },
        text = {
            Text(
                text = message,
                modifier = Modifier.semantics {
                    contentDescription = "Dialog message: $message"
                }
            )
        },
        confirmButton = {
            TextButton(
                onClick = onConfirm,
                modifier = Modifier.semantics {
                    contentDescription = if (isDestructive) {
                        "Confirm destructive action: $confirmButtonText"
                    } else {
                        "Confirm: $confirmButtonText"
                    }
                }
            ) {
                Text(
                    text = confirmButtonText,
                    color = if (isDestructive) MaterialTheme.colorScheme.error else MaterialTheme.colorScheme.primary
                )
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss,
                modifier = Modifier.semantics {
                    contentDescription = "Cancel: $dismissButtonText"
                }
            ) {
                Text(dismissButtonText)
            }
        }
    )
}

/**
 * Reusable input dialog with validation
 */
@Composable
fun InputDialog(
    title: String,
    label: String,
    initialValue: String = "",
    placeholder: String = "",
    keyboardType: KeyboardType = KeyboardType.Text,
    validator: (String) -> ValidationUtils.ValidationResult = { ValidationUtils.ValidationResult.success() },
    onConfirm: (String) -> Unit,
    onDismiss: () -> Unit
) {
    var value by remember { mutableStateOf(initialValue) }
    var validationResult by remember { mutableStateOf(ValidationUtils.ValidationResult.success()) }
    
    // Validate on value change
    LaunchedEffect(value) {
        validationResult = validator(value)
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = {
            Column {
                OutlinedTextField(
                    value = value,
                    onValueChange = { value = it },
                    label = { Text(label) },
                    placeholder = { Text(placeholder) },
                    keyboardOptions = KeyboardOptions(keyboardType = keyboardType),
                    isError = !validationResult.isValid,
                    supportingText = if (!validationResult.isValid) {
                        { Text(validationResult.errorMessage ?: "", color = MaterialTheme.colorScheme.error) }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onConfirm(value.trim()) },
                enabled = value.isNotBlank() && validationResult.isValid
            ) {
                Text(ButtonLabels.ADD)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(ButtonLabels.CANCEL)
            }
        }
    )
}

/**
 * Reusable two-field input dialog (e.g., for pasture name and temperature)
 */
@Composable
fun TwoFieldInputDialog(
    title: String,
    field1Label: String,
    field2Label: String,
    field1InitialValue: String = "",
    field2InitialValue: String = "",
    field1KeyboardType: KeyboardType = KeyboardType.Text,
    field2KeyboardType: KeyboardType = KeyboardType.Text,
    field1Validator: (String) -> ValidationUtils.ValidationResult = { ValidationUtils.ValidationResult.success() },
    field2Validator: (String) -> ValidationUtils.ValidationResult = { ValidationUtils.ValidationResult.success() },
    onConfirm: (String, String) -> Unit,
    onDismiss: () -> Unit
) {
    var field1Value by remember { mutableStateOf(field1InitialValue) }
    var field2Value by remember { mutableStateOf(field2InitialValue) }
    var field1ValidationResult by remember { mutableStateOf(ValidationUtils.ValidationResult.success()) }
    var field2ValidationResult by remember { mutableStateOf(ValidationUtils.ValidationResult.success()) }
    
    // Validate fields on value change
    LaunchedEffect(field1Value) {
        field1ValidationResult = field1Validator(field1Value)
    }
    
    LaunchedEffect(field2Value) {
        field2ValidationResult = field2Validator(field2Value)
    }
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedTextField(
                    value = field1Value,
                    onValueChange = { field1Value = it },
                    label = { Text(field1Label) },
                    keyboardOptions = KeyboardOptions(keyboardType = field1KeyboardType),
                    isError = !field1ValidationResult.isValid,
                    supportingText = if (!field1ValidationResult.isValid) {
                        { Text(field1ValidationResult.errorMessage ?: "", color = MaterialTheme.colorScheme.error) }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
                
                OutlinedTextField(
                    value = field2Value,
                    onValueChange = { field2Value = it },
                    label = { Text(field2Label) },
                    keyboardOptions = KeyboardOptions(keyboardType = field2KeyboardType),
                    isError = !field2ValidationResult.isValid,
                    supportingText = if (!field2ValidationResult.isValid) {
                        { Text(field2ValidationResult.errorMessage ?: "", color = MaterialTheme.colorScheme.error) }
                    } else null,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onConfirm(field1Value.trim(), field2Value.trim()) },
                enabled = field1Value.isNotBlank() && 
                         field2Value.isNotBlank() && 
                         field1ValidationResult.isValid && 
                         field2ValidationResult.isValid
            ) {
                Text(ButtonLabels.ADD)
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(ButtonLabels.CANCEL)
            }
        }
    )
}

/**
 * Loading dialog component
 */
@Composable
fun LoadingDialog(
    message: String = "Loading...",
    onDismiss: (() -> Unit)? = null
) {
    AlertDialog(
        onDismissRequest = { onDismiss?.invoke() },
        title = { Text("Please Wait") },
        text = {
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp)
                )
                Text(message)
            }
        },
        confirmButton = { },
        dismissButton = if (onDismiss != null) {
            {
                TextButton(onClick = onDismiss) {
                    Text(ButtonLabels.CANCEL)
                }
            }
        } else null
    )
}

/**
 * Error dialog component
 */
@Composable
fun ErrorDialog(
    title: String = "Error",
    message: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = { Text(message) },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(ButtonLabels.CLOSE)
            }
        }
    )
}

/**
 * Success dialog component
 */
@Composable
fun SuccessDialog(
    title: String = "Success",
    message: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(title) },
        text = { Text(message) },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(ButtonLabels.CLOSE)
            }
        }
    )
}

/**
 * Export retry dialog component
 */
@Composable
fun ExportRetryDialog(
    onDismiss: () -> Unit,
    onRetry: (String) -> Unit,
    newSheetsId: String,
    onNewSheetsIdChange: (String) -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Export Failed") },
        text = {
            Column {
                Text(
                    text = "The export failed. You can enter a new Google Sheets ID to retry the export. Note: The logbook entry will not be updated.",
                    style = MaterialTheme.typography.bodyMedium
                )
                Spacer(modifier = Modifier.height(16.dp))
                OutlinedTextField(
                    value = newSheetsId,
                    onValueChange = onNewSheetsIdChange,
                    label = { Text("New Google Sheets ID") },
                    placeholder = { Text("Enter new spreadsheet ID") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = { onRetry(newSheetsId) },
                enabled = newSheetsId.isNotBlank()
            ) {
                Text("Retry Export")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
