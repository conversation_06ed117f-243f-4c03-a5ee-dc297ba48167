package com.example.finalapp.viewmodel

import android.content.Context
import android.util.Base64
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.finalapp.model.Job
import com.example.finalapp.model.Pasture
import com.example.finalapp.model.PilotInfo
import com.example.finalapp.model.Survey
import com.example.finalapp.model.SurveyData
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "job_details")

/**
 * JobViewModel manages job-level data storage and operations.
 *
 * Data Storage Structure:
 * - PILOT_INFO: Map<String, PilotInfo> - Maps jobId to pilot information
 * - SELECTED_JOB: Job? - Currently selected job for operations
 * - COMPLETED_JOBS: List<Job> - All completed jobs with their basic metadata
 *
 * Note: Detailed job data (pastures, history, survey responses) are stored
 * separately in AppDataStore to avoid data duplication and improve performance.
 */
class JobViewModel : ViewModel() {
    private val _pilotInfo = MutableStateFlow<Map<String, PilotInfo>>(emptyMap())
    val pilotInfo: StateFlow<Map<String, PilotInfo>> = _pilotInfo.asStateFlow()

    private val _selectedJob = MutableStateFlow<Job?>(null)
    val selectedJob: StateFlow<Job?> = _selectedJob.asStateFlow()

    private val _completedJobs = MutableStateFlow<List<Job>>(emptyList())
    val completedJobs: StateFlow<List<Job>> = _completedJobs.asStateFlow()

    private lateinit var dataStore: DataStore<Preferences>
    private val json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
    }

    // DataStore Keys
    private val PILOT_INFO = stringPreferencesKey("pilot_info")
    private val SELECTED_JOB = stringPreferencesKey("selected_job")
    private val COMPLETED_JOBS = stringPreferencesKey("completed_jobs")

    fun initialize(context: Context) {
        dataStore = context.dataStore
        loadSavedJob()
    }

    private fun loadSavedJob() {
        viewModelScope.launch {
            dataStore.data.collect { preferences ->
                // Load pilot info
                val pilotInfoJson = preferences[PILOT_INFO] ?: "{}"
                try {
                    val pilotInfoMap = json.decodeFromString<Map<String, PilotInfo>>(pilotInfoJson)
                    _pilotInfo.value = pilotInfoMap
                } catch (e: Exception) {
                    _pilotInfo.value = emptyMap()
                }

                // Load selected job
                val selectedJobJson = preferences[SELECTED_JOB]
                if (selectedJobJson != null) {
                    try {
                        _selectedJob.value = json.decodeFromString<Job>(selectedJobJson)
                    } catch (e: Exception) {
                        _selectedJob.value = null
                    }
                }

                // Load completed jobs
                val completedJobsJson = preferences[COMPLETED_JOBS] ?: "[]"
                try {
                    _completedJobs.value = json.decodeFromString<List<Job>>(completedJobsJson)
                } catch (e: Exception) {
                    _completedJobs.value = emptyList()
                }
            }
        }
    }

    fun createJobFromSurvey(survey: Survey, pilotInfo: PilotInfo): Job {
        // Create new job with unique data
        val job = Job(
            name = survey.id ?: "New Job",
            mapImage = survey.image?.data?.let { imageData ->
                // Convert List<Int> to ByteArray
                val byteArray = ByteArray(imageData.size) { index -> imageData[index].toByte() }
                Base64.encodeToString(byteArray, Base64.DEFAULT)
            } ?: "",
            mapPdf = survey.pdf?.data?.let { pdfData ->
                // Convert List<Int> to ByteArray
                val byteArray = ByteArray(pdfData.size) { index -> pdfData[index].toByte() }
                Base64.encodeToString(byteArray, Base64.DEFAULT)
            } ?: "",
            coordinates = survey.coordinates ?: "",
            gateCode = survey.gate_code ?: "",
            additionalNotes = survey.notes ?: "",
            vehicleRequirements = survey.driving_instructions ?: "",
            pilotInfo = pilotInfo,
            googleSheetsId = survey.google_sheets_id?.trim()
        )

        // Add to completed jobs
        addCompletedJob(job)
        return job
    }

    fun setPilotInfo(jobId: String, pilotName: String, droneName: String) {
        val pilotInfo = PilotInfo(pilotName, droneName)
        _pilotInfo.update { currentMap ->
            currentMap + (jobId to pilotInfo)
        }
        viewModelScope.launch {
            dataStore.edit { preferences ->
                preferences[PILOT_INFO] = json.encodeToString(_pilotInfo.value)
            }
        }
    }

    fun getPilotInfo(jobId: String): PilotInfo? {
        return _pilotInfo.value[jobId]
    }

    fun setSelectedJob(job: Job) {
        _selectedJob.value = job
        viewModelScope.launch {
            dataStore.edit { preferences ->
                preferences[SELECTED_JOB] = json.encodeToString(job)
            }
        }
    }

    fun loadJobForSurvey(jobId: String) {
        val job = _completedJobs.value.find { it.id == jobId } ?: return
        setSelectedJob(job)
    }

    fun addCompletedJob(job: Job) {
        _completedJobs.update { currentList ->
            if (!currentList.any { it.id == job.id }) {
                currentList + job
            } else {
                currentList
            }
        }
        viewModelScope.launch {
            dataStore.edit { preferences ->
                preferences[COMPLETED_JOBS] = json.encodeToString(_completedJobs.value)
            }
        }
    }

    fun completeSurvey(jobId: String, pastureCounts: Map<String, Int>, notes: String) {
        val job = _completedJobs.value.find { it.id == jobId } ?: return
        val updatedJob = job.copy(
            surveyData = SurveyData(
                pastureCounts = pastureCounts,
                notes = notes
            )
        )
        _completedJobs.update { currentList ->
            currentList.map { if (it.id == jobId) updatedJob else it }
        }
        // Save to DataStore
        viewModelScope.launch {
            dataStore.edit { preferences ->
                preferences[COMPLETED_JOBS] = json.encodeToString(_completedJobs.value)
            }
        }
    }

    fun canExportJob(jobId: String): Boolean {
        val job = _completedJobs.value.find { it.id == jobId } ?: return false
        return job.surveyData != null && job.pastures.isNotEmpty()
    }

    fun exportJob(jobId: String) {
        val job = _completedJobs.value.find { it.id == jobId } ?: return
        if (!canExportJob(jobId)) return

        // TODO: Implement export logic
        // 1. Export job details
        // 2. Export pasture data
        // 3. Export survey data
        // 4. Update Google Sheets if googleSheetsId is present
    }

    fun clearAllData() {
        viewModelScope.launch {
            dataStore.edit { preferences ->
                preferences.clear()
            }
            _pilotInfo.value = emptyMap()
            _selectedJob.value = null
            _completedJobs.value = emptyList()
        }
    }

    fun clearJobData(jobId: String) {
        viewModelScope.launch {
            // Remove pilot info for the job
            _pilotInfo.update { currentMap ->
                currentMap - jobId
            }
            dataStore.edit { preferences ->
                preferences[PILOT_INFO] = json.encodeToString(_pilotInfo.value)
            }

            // Remove from completed jobs
            _completedJobs.update { currentList ->
                currentList.filter { it.id != jobId }
            }
            dataStore.edit { preferences ->
                preferences[COMPLETED_JOBS] = json.encodeToString(_completedJobs.value)
            }

            // If the cleared job was selected, clear selection
            if (_selectedJob.value?.id == jobId) {
                _selectedJob.value = null
                dataStore.edit { preferences ->
                    preferences.remove(SELECTED_JOB)
                }
            }
        }
    }
}