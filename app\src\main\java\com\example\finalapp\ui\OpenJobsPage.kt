package com.example.finalapp.ui

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.finalapp.model.Job
import com.example.finalapp.viewmodel.CounterViewModel
import com.example.finalapp.viewmodel.JobViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.finalapp.ui.PreDriveChecklistDialog
import com.example.finalapp.ui.PostJobChecklistDialog
import com.example.finalapp.ui.components.ExportRetryDialog

// Helper function to capitalize words
private fun String.capitalizeWords(): String {
    return this.split(" ").joinToString(" ") { word ->
        word.replaceFirstChar { it.uppercase() }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun OpenJobsPage(
    viewModel: CounterViewModel,
    onNavigateToCounter: (Job) -> Unit,
    onNavigateToPilotForm: () -> Unit
) {
    var showExportProgress by remember { mutableStateOf(false) }
    var showExportError by remember { mutableStateOf(false) }
    var jobToExport by remember { mutableStateOf<Job?>(null) }
    var showPostJobChecklist by remember { mutableStateOf(false) }

    // Export retry dialog states
    var showExportRetryDialog by remember { mutableStateOf(false) }
    var exportRetryJobId by remember { mutableStateOf<String?>(null) }
    var exportRetryNewSheetsId by remember { mutableStateOf("") }
    val allJobs by viewModel.allJobs.collectAsState()
    val jobsWithCompletedSurveys by viewModel.jobsWithCompletedSurveys.collectAsState()
    val jobSurveyStates by viewModel.jobSurveyStates.collectAsState()
    val jobViewModel: JobViewModel = viewModel()
    val checklistCompletion by viewModel.checklistCompletion.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Jobs") },
                actions = {
                    Button(
                        onClick = onNavigateToPilotForm,
                        modifier = Modifier.padding(end = 16.dp)
                    ) {
                        Text("Add New Job")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(allJobs) { job ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable {
                                // Navigate directly to counter screen when job is clicked
                                onNavigateToCounter(job)
                            }
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Column {
                                    Text(
                                        text = job.name,
                                        style = MaterialTheme.typography.titleMedium
                                    )
                                    Text(
                                        text = "Status: ${job.status}",
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                    if (job.coordinates.isNotEmpty()) {
                                        Text(
                                            text = "Location: ${job.coordinates}",
                                            style = MaterialTheme.typography.bodyMedium
                                        )
                                    }

                                    // Show export status
                                    val isExported = jobSurveyStates[job.id]?.second ?: false
                                    if (isExported) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                                        ) {
                                            Icon(
                                                Icons.Default.Lock,
                                                contentDescription = "Exported",
                                                modifier = Modifier.size(16.dp),
                                                tint = MaterialTheme.colorScheme.error
                                            )
                                            Text(
                                                text = "Exported (Read Only)",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.error
                                            )
                                        }
                                    }
                                }

                                // Show appropriate button based on status
                                val isExported = jobSurveyStates[job.id]?.second ?: false
                                if (jobsWithCompletedSurveys.contains(job.id) && !isExported) {
                                    Button(
                                        onClick = {
                                            jobToExport = job
                                            showPostJobChecklist = true
                                        }
                                    ) {
                                        Text("Export")
                                    }
                                } else if (isExported) {
                                    OutlinedButton(
                                        onClick = {
                                            onNavigateToCounter(job)
                                        }
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.spacedBy(4.dp)
                                        ) {
                                            Icon(
                                                Icons.Default.Visibility,
                                                contentDescription = "View",
                                                modifier = Modifier.size(16.dp)
                                            )
                                            Text("View Summary")
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    if (showExportProgress) {
        AlertDialog(
            onDismissRequest = { },
            title = { Text("Exporting Data") },
            text = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text("Please wait while we export your data...")
                }
            },
            confirmButton = { }
        )
    }

    if (showExportError) {
        AlertDialog(
            onDismissRequest = { showExportError = false },
            title = { Text("Export Failed") },
            text = { Text("Failed to export data to Google Sheets. Please try again.") },
            confirmButton = {
                TextButton(onClick = { showExportError = false }) {
                    Text("OK")
                }
            }
        )
    }
    
    // Post-Job Checklist Dialog
    if (showPostJobChecklist && jobToExport != null) {
        PostJobChecklistDialog(
            onDismiss = { showPostJobChecklist = false },
            onComplete = {
                showPostJobChecklist = false
                jobToExport?.let { job ->
                    showExportProgress = true
                    viewModel.exportJobToGoogleSheets(job) { success ->
                        showExportProgress = false
                        if (!success) {
                            // Show export retry dialog on failure
                            exportRetryJobId = job.id
                            exportRetryNewSheetsId = ""
                            showExportRetryDialog = true
                        }
                    }
                }
                jobToExport = null
            },
            jobViewModel = jobViewModel,
            counterViewModel = viewModel
        )
    }

    // Export retry dialog
    if (showExportRetryDialog && exportRetryJobId != null) {
        ExportRetryDialog(
            onDismiss = {
                showExportRetryDialog = false
                exportRetryJobId = null
                exportRetryNewSheetsId = ""
            },
            onRetry = { newSheetsId ->
                exportRetryJobId?.let { jobId ->
                    // Find the job by ID
                    val jobToRetry = allJobs.find { it.id == jobId }
                    jobToRetry?.let { job ->
                        showExportProgress = true
                        viewModel.exportJobToGoogleSheetsWithCustomId(job, newSheetsId) { success ->
                            showExportProgress = false
                            showExportRetryDialog = false
                            exportRetryJobId = null
                            exportRetryNewSheetsId = ""
                            if (!success) {
                                showExportError = true
                            }
                            // If successful, the job will be removed from the list automatically
                        }
                    }
                }
            },
            newSheetsId = exportRetryNewSheetsId,
            onNewSheetsIdChange = { exportRetryNewSheetsId = it }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun JobCard(
    job: Job,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = job.name,
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = "Status: ${job.status}",
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
} 