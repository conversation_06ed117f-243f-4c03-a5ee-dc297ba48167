package com.example.finalapp

import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.core.view.WindowCompat
import com.example.finalapp.ui.*
import com.example.finalapp.ui.theme.FinalAppTheme
import com.example.finalapp.viewmodel.CounterViewModel
import com.example.finalapp.viewmodel.JobViewModel
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    internal lateinit var counterViewModel: CounterViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d("MainActivity", "onCreate called")
        enableEdgeToEdge()
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Add keyboard configuration
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        
        setContent {
            FinalAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen()
                }
            }
        }
    }

    override fun onStop() {
        super.onStop()
        Log.d("MainActivity", "onStop called")
        if (::counterViewModel.isInitialized) {
            Log.d("MainActivity", "Saving data before app closure")
            counterViewModel.onAppClose()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("MainActivity", "onDestroy called")
        if (::counterViewModel.isInitialized) {
            Log.d("MainActivity", "Saving data in onDestroy")
            counterViewModel.onAppClose()
        }
    }
}

@Composable
fun MainScreen() {
    val navController = rememberNavController()
    val counterViewModel: CounterViewModel = viewModel()
    val jobViewModel: JobViewModel = viewModel()
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    // Store the ViewModel reference in the activity
    (context as? MainActivity)?.counterViewModel = counterViewModel

    // Initialize JobViewModel with context
    LaunchedEffect(Unit) {
        jobViewModel.initialize(context)
    }

    NavHost(navController = navController, startDestination = "surveys") {
        composable("surveys") {
            OpenJobsPage(
                viewModel = counterViewModel,
                onNavigateToCounter = { job ->
                    scope.launch {
                        counterViewModel.selectJob(job, skipDataStoreLoad = false)

                        // Check if job has completed survey response (pilot form completed)
                        val jobsWithCompletedSurveys = counterViewModel.jobsWithCompletedSurveys.first()
                        val hasSurveyResponse = jobsWithCompletedSurveys.contains(job.id)

                        // Check if job is exported
                        val jobSurveyStates = counterViewModel.jobSurveyStates.first()
                        val isExported = jobSurveyStates[job.id]?.second ?: false

                        // Show read-only summary if job has completed survey response or is exported
                        if (hasSurveyResponse || isExported) {
                            navController.navigate("readonly_summary")
                        } else {
                            navController.navigate("counters")
                        }
                    }
                },
                onNavigateToPilotForm = {
                    navController.navigate("pilot_form")
                }
            )
        }
        composable("pilot_form") {
            AddJobScreen(
                onFormComplete = { 
                    scope.launch {
                        navController.navigate("surveys") {
                            popUpTo("pilot_form") { inclusive = true }
                        }
                    }
                },
                onNavigateToSurveys = {
                    navController.navigate("surveys") {
                        popUpTo("pilot_form") { inclusive = true }
                    }
                },
                viewModel = jobViewModel
            )
        }
        composable("counters") {
            CounterScreen(
                viewModel = counterViewModel,
                jobViewModel = jobViewModel,
                onNavigateToSurvey = { 
                    navController.navigate("survey") {
                        popUpTo("counters") { inclusive = true }
                    }
                },
                onNavigateToPilotForm = {
                    navController.navigate("pilot_form") {
                        popUpTo("counters") { inclusive = true }
                    }
                },
                onNavigateToSurveys = {
                    navController.navigate("surveys") {
                        popUpTo("counters") { inclusive = true }
                    }
                }
            )
        }
        composable("survey") {
            PilotSurvey(
                onConfirm = { response ->
                    scope.launch {
                        counterViewModel.updateSurveyResponse(response)
                        navController.navigate("surveys") {
                            popUpTo("survey") { inclusive = true }
                        }
                    }
                },
                viewModel = counterViewModel
            )
        }
        composable("readonly_summary") {
            ReadOnlySummaryScreen(
                onNavigateBack = {
                    navController.navigate("surveys") {
                        popUpTo("readonly_summary") { inclusive = true }
                    }
                },
                viewModel = counterViewModel,
                jobViewModel = jobViewModel
            )
        }
    }
}