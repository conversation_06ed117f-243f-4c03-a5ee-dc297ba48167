package com.example.finalapp.ui

import android.content.Context
import android.graphics.Bitmap
import android.graphics.pdf.PdfRenderer
import android.os.ParcelFileDescriptor
import android.util.Base64
import androidx.compose.foundation.Image
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ZoomIn
import androidx.compose.material.icons.filled.ZoomOut
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import java.io.File
import java.io.FileOutputStream
import androidx.compose.material.icons.filled.Close

@Composable
fun ZoomableImage(
    bitmap: Bitmap,
    modifier: Modifier = Modifier,
    contentDescription: String? = null
) {
    var scale by remember { mutableStateOf(1f) }
    var offset by remember { mutableStateOf(androidx.compose.ui.geometry.Offset.Zero) }
    
    Box(
        modifier = modifier
            .pointerInput(Unit) {
                detectTransformGestures { _, pan, zoom, _ ->
                    scale = (scale * zoom).coerceIn(0.5f..3f)
                    offset += pan
                }
            }
    ) {
        Image(
            bitmap = bitmap.asImageBitmap(),
            contentDescription = contentDescription,
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer(
                    scaleX = scale,
                    scaleY = scale,
                    translationX = offset.x,
                    translationY = offset.y
                ),
            contentScale = ContentScale.Fit
        )
        
        // Zoom controls
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(8.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            IconButton(
                onClick = { scale = (scale * 1.2f).coerceIn(0.5f..3f) }
            ) {
                Icon(Icons.Default.ZoomIn, contentDescription = "Zoom In")
            }
            IconButton(
                onClick = { scale = (scale / 1.2f).coerceIn(0.5f..3f) }
            ) {
                Icon(Icons.Default.ZoomOut, contentDescription = "Zoom Out")
            }
        }
    }
}

@Composable
fun PdfViewer(
    pdfBase64: String,
    modifier: Modifier = Modifier,
    onError: (String) -> Unit = {}
) {
    val context = LocalContext.current
    var isLoading by remember { mutableStateOf(true) }
    var error by remember { mutableStateOf<String?>(null) }
    var pdfPages by remember { mutableStateOf<List<Bitmap>>(emptyList()) }

    LaunchedEffect(pdfBase64) {
        try {
            isLoading = true
            error = null
            
            if (pdfBase64.isNotEmpty()) {
                // Decode base64 to byte array
                val pdfBytes = Base64.decode(pdfBase64, Base64.DEFAULT)
                
                // Create temporary file
                val tempFile = File.createTempFile("pdf_", ".pdf", context.cacheDir)
                FileOutputStream(tempFile).use { fos ->
                    fos.write(pdfBytes)
                }
                
                // Use PdfRenderer to render pages
                val fileDescriptor = ParcelFileDescriptor.open(tempFile, ParcelFileDescriptor.MODE_READ_ONLY)
                val pdfRenderer = PdfRenderer(fileDescriptor)
                
                val pages = mutableListOf<Bitmap>()
                for (i in 0 until pdfRenderer.pageCount) {
                    val page = pdfRenderer.openPage(i)
                    val bitmap = Bitmap.createBitmap(
                        page.width * 2, // Scale up for better quality
                        page.height * 2,
                        Bitmap.Config.ARGB_8888
                    )
                    page.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY)
                    pages.add(bitmap)
                    page.close()
                }
                pdfRenderer.close()
                fileDescriptor.close()
                
                pdfPages = pages
                isLoading = false
            } else {
                isLoading = false
            }
        } catch (e: Exception) {
            error = e.message
            isLoading = false
            onError(e.message ?: "Failed to load PDF")
        }
    }

    Box(modifier = modifier) {
        when {
            isLoading -> {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
            error != null -> {
                Text(
                    text = "Error loading PDF: $error",
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
            pdfPages.isNotEmpty() -> {
                LazyColumn {
                    items(pdfPages) { pageBitmap ->
                        ZoomableImage(
                            bitmap = pageBitmap,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(800.dp)
                                .padding(8.dp)
                                .clip(MaterialTheme.shapes.medium),
                            contentDescription = "PDF Page"
                        )
                    }
                }
            }
            else -> {
                Text(
                    text = "No PDF content available",
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

@Composable
fun PdfViewerDialog(
    pdfBase64: String,
    onDismiss: () -> Unit
) {
    androidx.compose.ui.window.Dialog(
        onDismissRequest = onDismiss,
        properties = androidx.compose.ui.window.DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Surface(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            color = MaterialTheme.colorScheme.background,
            shape = MaterialTheme.shapes.medium
        ) {
            Column {
                // Header with close button
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(onClick = onDismiss) {
                        Icon(Icons.Default.Close, contentDescription = "Close")
                    }
                }
                
                // PDF content
                PdfViewer(
                    pdfBase64 = pdfBase64,
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                )
            }
        }
    }
} 