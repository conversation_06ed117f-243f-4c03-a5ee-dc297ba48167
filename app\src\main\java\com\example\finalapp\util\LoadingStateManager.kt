package com.example.finalapp.util

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * Manages loading states for different operations
 */
class LoadingStateManager {
    
    private val _loadingStates = MutableStateFlow<Map<String, Boolean>>(emptyMap())
    val loadingStates: StateFlow<Map<String, Boolean>> = _loadingStates.asStateFlow()
    
    private val _globalLoading = MutableStateFlow(false)
    val globalLoading: StateFlow<Boolean> = _globalLoading.asStateFlow()
    
    /**
     * Set loading state for a specific operation
     */
    fun setLoading(operation: String, isLoading: Boolean) {
        _loadingStates.value = _loadingStates.value.toMutableMap().apply {
            if (isLoading) {
                this[operation] = true
            } else {
                remove(operation)
            }
        }
        updateGlobalLoading()
    }
    
    /**
     * Check if a specific operation is loading
     */
    fun isLoading(operation: String): Boolean {
        return _loadingStates.value[operation] == true
    }
    
    /**
     * Check if any operation is loading
     */
    fun isAnyLoading(): Boolean {
        return _loadingStates.value.isNotEmpty()
    }
    
    /**
     * Clear all loading states
     */
    fun clearAll() {
        _loadingStates.value = emptyMap()
        _globalLoading.value = false
    }
    
    /**
     * Execute operation with loading state management
     */
    suspend inline fun <T> withLoading(
        operation: String,
        block: suspend () -> T
    ): T {
        setLoading(operation, true)
        try {
            return block()
        } finally {
            setLoading(operation, false)
        }
    }
    
    private fun updateGlobalLoading() {
        _globalLoading.value = _loadingStates.value.isNotEmpty()
    }
    
    companion object {
        // Common operation names
        const val ADDING_PASTURE = "adding_pasture"
        const val DELETING_PASTURE = "deleting_pasture"
        const val ADDING_COUNTER = "adding_counter"
        const val UPDATING_COUNTER = "updating_counter"
        const val DELETING_COUNTER = "deleting_counter"
        const val SAVING_DATA = "saving_data"
        const val LOADING_DATA = "loading_data"
        const val EXPORTING_DATA = "exporting_data"
        const val FINISHING_SURVEY = "finishing_survey"
        const val CANCELLING_SURVEY = "cancelling_survey"
    }
}

/**
 * Loading state for UI components
 */
data class LoadingState(
    val isLoading: Boolean = false,
    val message: String = "Loading...",
    val canCancel: Boolean = false
) {
    companion object {
        val IDLE = LoadingState()
        
        fun loading(message: String = "Loading...", canCancel: Boolean = false) = 
            LoadingState(true, message, canCancel)
    }
}

/**
 * Async operation state
 */
sealed class AsyncState<out T> {
    object Idle : AsyncState<Nothing>()
    object Loading : AsyncState<Nothing>()
    data class Success<T>(val data: T) : AsyncState<T>()
    data class Error(val message: String, val throwable: Throwable? = null) : AsyncState<Nothing>()
    
    val isLoading: Boolean get() = this is Loading
    val isSuccess: Boolean get() = this is Success
    val isError: Boolean get() = this is Error
    val isIdle: Boolean get() = this is Idle
    
    fun getDataOrNull(): T? = if (this is Success) data else null
    fun getErrorOrNull(): String? = if (this is Error) message else null
}

/**
 * Extension functions for easier state management
 */
fun <T> AsyncState<T>.onSuccess(action: (T) -> Unit): AsyncState<T> {
    if (this is AsyncState.Success) action(data)
    return this
}

fun <T> AsyncState<T>.onError(action: (String, Throwable?) -> Unit): AsyncState<T> {
    if (this is AsyncState.Error) action(message, throwable)
    return this
}

fun <T> AsyncState<T>.onLoading(action: () -> Unit): AsyncState<T> {
    if (this is AsyncState.Loading) action()
    return this
}
