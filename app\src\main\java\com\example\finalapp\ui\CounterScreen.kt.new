package com.example.finalapp.ui

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.rounded.DragHandle
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.finalapp.model.Counter
import com.example.finalapp.model.CounterChange
import com.example.finalapp.model.Pasture
import com.example.finalapp.viewmodel.CounterViewModel
import com.example.finalapp.viewmodel.JobViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun CounterScreen(
    viewModel: CounterViewModel = viewModel(),
    jobViewModel: JobViewModel,
    onNavigateToPilotForm: () -> Unit
) {
    val pastures by viewModel.pastures.collectAsState()
    val selectedPastureIndex by viewModel.selectedPastureIndex.collectAsState()
    val history by viewModel.history.collectAsState()
    val isSurveyStarted by viewModel.isSurveyStarted.collectAsState()
    val isExported by viewModel.isExported.collectAsState()
    var showAddPastureDialog by remember { mutableStateOf(false) }
    var showAddCounterDialog by remember { mutableStateOf(false) }
    var showHistoryDialog by remember { mutableStateOf(false) }
    var showJobDetailsDialog by remember { mutableStateOf(false) }
    val showPostFlightSurvey by viewModel.showPostFlightSurvey.collectAsState()
    var showFinishSurveyConfirmation by remember { mutableStateOf(false) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Sky Senderos") },
                actions = {
                    IconButton(onClick = { showJobDetailsDialog = true }) {
                        Icon(Icons.Default.Info, contentDescription = "Job Details")
                    }
                    IconButton(onClick = { showHistoryDialog = true }) {
                        Icon(Icons.Default.History, contentDescription = "History")
                    }
                    IconButton(
                        onClick = { showAddPastureDialog = true },
                        enabled = isSurveyStarted
                    ) {
                        Icon(
                            Icons.Default.Add,
                            contentDescription = "Add Pasture",
                            modifier = Modifier.alpha(if (isSurveyStarted) 1f else 0.5f)
                        )
                    }
                }
            )
        }
    ) { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            // Pasture selection
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.2f)
                    .padding(8.dp)
            ) {
                items(pastures) { pasture ->
                    PastureItem(
                        pasture = pasture,
                        isSelected = pastures.indexOf(pasture) == selectedPastureIndex,
                        onClick = { viewModel.selectPasture(pastures.indexOf(pasture)) }
                    )
                }
            }

            // Counter grid for selected pasture
            selectedPastureIndex?.let { index ->
                val pasture = pastures[index]
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(0.8f)
                        .padding(8.dp)
                ) {
                    // Pasture info
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                            Text(
                                text = pasture.name,
                                style = MaterialTheme.typography.titleLarge
                            )
                            Text(
                                text = "Temperature: ${pasture.temperature}°C",
                                style = MaterialTheme.typography.bodyLarge
                            )
                            Text(
                                text = "Created: ${SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
                                    .format(Date(pasture.creationTimestamp))}",
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }

                    // Add counter button
                    if (isSurveyStarted) {
                        Button(
                            onClick = { showAddCounterDialog = true },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 8.dp)
                        ) {
                            Icon(Icons.Default.Add, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Add Counter")
                        }
                    }

                    // Counter grid
                    LazyVerticalGrid(
                        columns = GridCells.Adaptive(minSize = 160.dp),
                        modifier = Modifier.weight(1f),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(
                            items = pasture.counters,
                            key = { it.name }
                        ) { counter ->
                            val counterIndex = pasture.counters.indexOf(counter)
                            CounterItem(
                                counter = counter,
                                onValueChange = { newValue ->
                                    viewModel.updateCounter(counterIndex, newValue)
                                },
                                onDelete = { viewModel.removeCounter(counterIndex) },
                                index = counterIndex
                            )
                        }
                    }
                }
            } ?: run {
                // No pasture selected
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = if (pastures.isEmpty()) "Add a pasture to begin" else "Select a pasture",
                        style = MaterialTheme.typography.bodyLarge
                    )
                }
            }

            // Survey buttons
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                if (!isSurveyStarted) {
                    Button(
                        onClick = { viewModel.startSurvey() }
                    ) {
                        Text("Start Survey")
                    }
                } else {
                    Button(
                        onClick = { showFinishSurveyConfirmation = true }
                    ) {
                        Text("Finish Survey")
                    }
                }
            }
        }
    }

    if (showAddPastureDialog) {
        AddPastureDialog(
            onDismiss = { showAddPastureDialog = false },
            onConfirm = { name, temperature ->
                viewModel.addPasture(name, temperature)
                showAddPastureDialog = false
            }
        )
    }

    if (showAddCounterDialog) {
        AddCounterDialog(
            onDismiss = { showAddCounterDialog = false },
            onConfirm = { name ->
                viewModel.addCounter(name)
                showAddCounterDialog = false
            }
        )
    }

    if (showHistoryDialog) {
        HistoryDialog(
            history = history,
            onDismiss = { showHistoryDialog = false },
            onUndo = { change ->
                // TODO: Implement undo for pasture counters
            }
        )
    }

    if (showJobDetailsDialog) {
        JobDetailsDialog(
            onDismiss = { showJobDetailsDialog = false },
            viewModel = jobViewModel
        )
    }

    if (showFinishSurveyConfirmation) {
        AlertDialog(
            onDismissRequest = { showFinishSurveyConfirmation = false },
            title = { Text("Finish Survey") },
            text = { Text("Are you sure you want to finish the survey?") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showFinishSurveyConfirmation = false
                        viewModel.setShowPostFlightSurvey(true)
                    }
                ) {
                    Text("Yes")
                }
            },
            dismissButton = {
                TextButton(onClick = { showFinishSurveyConfirmation = false }) {
                    Text("No")
                }
            }
        )
    }

    if (showPostFlightSurvey) {
        // Show the post-flight survey UI here (as before)
        // ...
        // When finished, call viewModel.setShowPostFlightSurvey(false)
    }

    // Observe export state changes
    LaunchedEffect(isExported) {
        if (isExported) {
            onNavigateToPilotForm()
        }
    }
}

@Composable
fun PastureItem(
    pasture: Pasture,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = pasture.name,
                    style = MaterialTheme.typography.titleMedium
                )
                Text(
                    text = "${pasture.counters.size} counters",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            Text(
                text = "${pasture.temperature}°C",
                style = MaterialTheme.typography.titleMedium
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun CounterItem(
    counter: Counter,
    onValueChange: (Int) -> Unit,
    onDelete: () -> Unit,
    index: Int
) {
    var customAmount by remember { mutableStateOf("1") }
    var showDeleteConfirmation by remember { mutableStateOf(false) }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Rounded.DragHandle,
                        contentDescription = "Drag to reorder",
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    Text(
                        text = counter.name,
                        style = MaterialTheme.typography.titleMedium
                    )
                }
                Text(
                    text = "Count: ${counter.value}",
                    style = MaterialTheme.typography.titleMedium
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(
                        onClick = { 
                            customAmount.toIntOrNull()?.let { amount ->
                                onValueChange(counter.value - amount)
                            }
                        },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Text("-", style = MaterialTheme.typography.titleLarge)
                    }
                    
                    TextField(
                        value = customAmount,
                        onValueChange = { customAmount = it },
                        keyboardOptions = androidx.compose.foundation.text.KeyboardOptions(
                            keyboardType = KeyboardType.Number
                        ),
                        modifier = Modifier.width(48.dp),
                        singleLine = true
                    )
                    
                    IconButton(
                        onClick = { 
                            customAmount.toIntOrNull()?.let { amount ->
                                onValueChange(counter.value + amount)
                            }
                        },
                        modifier = Modifier.size(32.dp)
                    ) {
                        Text("+", style = MaterialTheme.typography.titleLarge)
                    }
                }
                
                IconButton(
                    onClick = { showDeleteConfirmation = true },
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(Icons.Default.Delete, contentDescription = "Delete")
                }
            }
        }
    }

    if (showDeleteConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = { Text("Delete Counter") },
            text = { Text("Are you sure you want to delete ${counter.name}?") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDelete()
                        showDeleteConfirmation = false
                    }
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirmation = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun CustomAmountDialog(
    onDismiss: () -> Unit,
    onIncrement: (Int) -> Unit,
    onDecrement: (Int) -> Unit
) {
    var amount by remember { mutableStateOf("1") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Custom Amount") },
        text = {
            Column {
                TextField(
                    value = amount,
                    onValueChange = { amount = it },
                    label = { Text("Amount") },
                    keyboardOptions = androidx.compose.foundation.text.KeyboardOptions(
                        keyboardType = KeyboardType.Number
                    )
                )
            }
        },
        confirmButton = {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TextButton(
                    onClick = { 
                        amount.toIntOrNull()?.let { onDecrement(it) }
                    },
                    enabled = amount.toIntOrNull() != null
                ) {
                    Text("-")
                }
                TextButton(
                    onClick = { 
                        amount.toIntOrNull()?.let { onIncrement(it) }
                    },
                    enabled = amount.toIntOrNull() != null
                ) {
                    Text("+")
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun HistoryDialog(
    history: List<CounterChange>,
    onDismiss: () -> Unit,
    onUndo: (CounterChange) -> Unit
) {
    val dateFormat = remember { SimpleDateFormat("HH:mm:ss", Locale.getDefault()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Recent Changes") },
        text = {
            Column {
                if (history.isEmpty()) {
                    Text("No recent changes")
                } else {
                    history.forEach { change ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Column {
                                Text("${change.counterName}: ${change.oldValue} → ${change.newValue}")
                                Text(
                                    dateFormat.format(Date(change.timestamp)),
                                    style = MaterialTheme.typography.bodySmall
                                )
                            }
                            TextButton(onClick = { onUndo(change) }) {
                                Text("Undo")
                            }
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddCounterDialog(
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    var name by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Add New Counter") },
        text = {
            TextField(
                value = name,
                onValueChange = { name = it },
                label = { Text("Counter Name") }
            )
        },
        confirmButton = {
            TextButton(
                onClick = { onConfirm(name) },
                enabled = name.isNotBlank()
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddPastureDialog(
    onDismiss: () -> Unit,
    onConfirm: (String, Double) -> Unit
) {
    var name by remember { mutableStateOf("") }
    var temperature by remember { mutableStateOf("0.0") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Add New Pasture") },
        text = {
            Column {
                TextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Pasture Name") }
                )
                TextField(
                    value = temperature,
                    onValueChange = { temperature = it },
                    label = { Text("Temperature (°C)") },
                    keyboardOptions = androidx.compose.foundation.text.KeyboardOptions(
                        keyboardType = KeyboardType.Number
                    )
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onConfirm(name, temperature.toDouble())
                },
                enabled = name.isNotBlank() && temperature.isNotBlank()
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
} 