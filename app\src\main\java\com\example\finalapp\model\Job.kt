package com.example.finalapp.model

import java.util.UUID
import kotlinx.serialization.Serializable

@Serializable
data class Job(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val status: String = "Active",
    val mapImage: String = "",
    val mapPdf: String = "",
    val creationTimestamp: Long = System.currentTimeMillis(),
    val coordinates: String = "",
    val gateCode: String = "",
    val additionalNotes: String = "",
    val vehicleRequirements: String = "",
    val pilotInfo: PilotInfo? = null,
    val googleSheetsId: String? = null,
    val surveyId: String? = null, // Store the database survey ID
    val pastures: List<Pasture> = emptyList(),
    val surveyData: SurveyData? = null,
    val apiDateTime: String? = null,
    val checklistCompletion: Set<String> = emptySet() // Track completed checklists
)

@Serializable
data class PilotInfo(
    val name: String,
    val droneName: String
)

@Serializable
data class SurveyData(
    val completionTimestamp: Long = System.currentTimeMillis(),
    val pastureCounts: Map<String, Int> = emptyMap(), // Map of pasture name to total count
    val notes: String = ""
) 