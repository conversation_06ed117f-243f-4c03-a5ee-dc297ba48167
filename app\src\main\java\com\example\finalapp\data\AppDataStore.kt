package com.example.finalapp.data

import android.content.Context
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import com.example.finalapp.model.ChecklistData
import com.example.finalapp.model.Counter
import com.example.finalapp.model.CounterChange
import com.example.finalapp.model.Pasture
import com.example.finalapp.model.SurveyResponse
import com.example.finalapp.model.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import kotlin.math.abs

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "app_data")

/**
 * AppDataStore manages persistent storage for job-related data.
 *
 * Data Storage Structure:
 * - JOB_PASTURES: Map<String, List<Pasture>> - Maps jobId to list of pastures
 * - JOB_HISTORY: Map<String, List<CounterChange>> - Maps "${jobId}_${pastureName}" to counter changes
 * - JOB_SURVEY_RESPONSES: Map<String, SurveyResponse> - Maps jobId to survey responses
 * - ACTIVE_JOBS: List<Job> - Currently active jobs
 * - Other job-specific metadata...
 *
 * History Key Format: "${jobId}_${pastureName}" ensures unique keys across jobs
 * and enables efficient cleanup when pastures are deleted.
 */
class AppDataStore(private val context: Context) {
    private val dataStore = context.dataStore
    private val json = Json {
        ignoreUnknownKeys = true
        coerceInputValues = true
    }

    // DataStore Keys
    private val ACTIVE_JOBS = stringPreferencesKey("active_jobs")
    private val COMPLETED_JOBS = stringPreferencesKey("completed_jobs")
    private val SELECTED_JOB = stringPreferencesKey("selected_job")
    private val JOB_PASTURES = stringPreferencesKey("job_pastures")
    private val JOB_SURVEY_RESPONSES = stringPreferencesKey("job_survey_responses")
    private val JOB_HISTORY = stringPreferencesKey("job_history")
    private val JOB_SURVEY_STATES = stringPreferencesKey("job_survey_states")
    private val SELECTED_PASTURE_INDEX = intPreferencesKey("selected_pasture_index")
    private val IS_NEW_JOB = booleanPreferencesKey("is_new_job")
    private val JOBS_WITH_COMPLETED_SURVEYS = stringPreferencesKey("jobs_with_completed_surveys")
    private val JOB_CHECKLIST_COMPLETION = stringPreferencesKey("job_checklist_completion") // Map<String, Set<String>>
    private val JOB_SHOW_POST_FLIGHT_SURVEY = stringPreferencesKey("job_show_post_flight_survey")
    private val JOB_CHECKLIST_DATA = stringPreferencesKey("job_checklist_data")

    companion object {
        private const val MAX_HISTORY_ITEMS_PER_JOB = 100  // Limit history to last 100 changes
        private const val MIN_CHANGE_THRESHOLD = 1  // Only record changes of 1 or more

        /**
         * Creates a history key for storing pasture-specific counter changes
         * Format: "${jobId}_${pastureName}"
         */
        fun createHistoryKey(jobId: String, pastureName: String): String = "${jobId}_${pastureName}"

        /**
         * Extracts jobId from a history key
         */
        fun extractJobIdFromHistoryKey(historyKey: String): String? {
            val parts = historyKey.split("_", limit = 2)
            return if (parts.size >= 2) parts[0] else null
        }

        /**
         * Extracts pasture name from a history key
         */
        fun extractPastureNameFromHistoryKey(historyKey: String): String? {
            val parts = historyKey.split("_", limit = 2)
            return if (parts.size >= 2) parts[1] else null
        }
    }

    // Save pastures
    suspend fun savePastures(pastures: List<Pasture>) {
        dataStore.edit { preferences ->
            preferences[JOB_PASTURES] = Json.encodeToString(pastures)
        }
    }

    // Get pastures
    val pastures: Flow<List<Pasture>> = dataStore.data.map { preferences ->
        val pasturesJson = preferences[JOB_PASTURES] ?: "[]"
        try {
            Json.decodeFromString(pasturesJson)
        } catch (e: Exception) {
            emptyList()
        }
    }

    // Save selected pasture index
    suspend fun saveSelectedPastureIndex(index: Int?) {
        dataStore.edit { preferences ->
            if (index != null) {
                preferences[SELECTED_PASTURE_INDEX] = index
            } else {
                preferences.remove(SELECTED_PASTURE_INDEX)
            }
        }
    }

    // Get selected pasture index
    val selectedPastureIndex: Flow<Int?> = dataStore.data.map { preferences ->
        preferences[SELECTED_PASTURE_INDEX]
    }

    // Save new job state
    suspend fun saveNewJobState(isNewJob: Boolean) {
        dataStore.edit { preferences ->
            preferences[IS_NEW_JOB] = isNewJob
        }
    }

    // Get new job state
    val isNewJob: Flow<Boolean> = dataStore.data.map { preferences ->
        preferences[IS_NEW_JOB] ?: true
    }

    // Jobs
    val activeJobs: Flow<List<Job>> = dataStore.data.map { preferences ->
        val jobsJson = preferences[ACTIVE_JOBS] ?: "[]"
        try {
            val jobs = Json.decodeFromString<List<Job>>(jobsJson)
            Log.d("AppDataStore", "=== Loading active jobs ===")
            Log.d("AppDataStore", "Loaded ${jobs.size} active jobs")
            jobs
        } catch (e: Exception) {
            Log.e("AppDataStore", "Error loading active jobs", e)
            emptyList()
        }
    }

    val selectedJob: Flow<Job?> = dataStore.data.map { preferences ->
        val jobJson = preferences[SELECTED_JOB]
        if (jobJson != null) {
            try {
                Json.decodeFromString(jobJson)
            } catch (e: Exception) {
                null
            }
        } else null
    }

    // Save active jobs
    suspend fun saveActiveJobs(jobs: List<Job>) {
        Log.d("AppDataStore", "=== Saving active jobs ===")
        dataStore.edit { preferences ->
            val jobsJson = Json.encodeToString(jobs)
            Log.d("AppDataStore", "Saving ${jobs.size} active jobs")
            preferences[ACTIVE_JOBS] = jobsJson
        }
    }

    // Save completed jobs
    suspend fun saveCompletedJobs(jobs: List<Job>) {
        dataStore.edit { preferences ->
            val jobsJson = Json.encodeToString(jobs)
            Log.d("AppDataStore", "Saving completed jobs count: ${jobs.size}")
            preferences[COMPLETED_JOBS] = jobsJson
        }
    }

    // Save jobs with completed surveys
    suspend fun saveJobsWithCompletedSurveys(jobIds: Set<String>) {
        dataStore.edit { preferences ->
            preferences[JOBS_WITH_COMPLETED_SURVEYS] = Json.encodeToString(jobIds)
        }
    }

    // Get completed jobs
    val completedJobs: Flow<List<Job>> = dataStore.data.map { preferences ->
        val jobsJson = preferences[COMPLETED_JOBS] ?: "[]"
        try {
            Json.decodeFromString(jobsJson)
        } catch (e: Exception) {
            emptyList()
        }
    }

    // Get jobs with completed surveys
    val jobsWithCompletedSurveys: Flow<Set<String>> = dataStore.data.map { preferences ->
        val jobIdsJson = preferences[JOBS_WITH_COMPLETED_SURVEYS] ?: "[]"
        Json.decodeFromString<Set<String>>(jobIdsJson)
    }

    suspend fun saveSelectedJob(job: Job?) {
        dataStore.edit { preferences ->
            if (job != null) {
                val jobJson = Json.encodeToString(job)
                Log.d("AppDataStore", "Saving selected job: ${job.id}")
                preferences[SELECTED_JOB] = jobJson
            } else {
                preferences.remove(SELECTED_JOB)
            }
        }
    }

    // Job Pastures
    val jobPastures: Flow<Map<String, List<Pasture>>> = dataStore.data.map { preferences ->
        val pasturesJson = preferences[JOB_PASTURES] ?: "{}"
        try {
            val pastures = Json.decodeFromString<Map<String, List<Pasture>>>(pasturesJson)
            Log.d("AppDataStore", "=== Loading job pastures ===")
            Log.d("AppDataStore", "Loaded ${pastures.size} jobs with pastures")
            pastures
        } catch (e: Exception) {
            Log.e("AppDataStore", "Error loading pastures", e)
            emptyMap()
        }
    }

    suspend fun saveJobPastures(pastures: Map<String, List<Pasture>>) {
        Log.d("AppDataStore", "=== Saving job pastures ===")
        dataStore.edit { preferences ->
            val pasturesJson = Json.encodeToString(pastures)
            Log.d("AppDataStore", "Saving ${pastures.size} jobs with pastures")
            preferences[JOB_PASTURES] = pasturesJson
        }
    }

    // Job-specific survey responses
    val jobSurveyResponses: Flow<Map<String, SurveyResponse>> = dataStore.data.map { preferences ->
        val responsesJson = preferences[JOB_SURVEY_RESPONSES] ?: "{}"
        val responses = Json.decodeFromString<Map<String, SurveyResponse>>(responsesJson)
        Log.d("AppDataStore", "Loading survey responses: $responses")
        responses.forEach { (jobId, response) ->
            Log.d("AppDataStore", "Job $jobId - startTimestamp: ${response.startTimestamp}, completionTimestamp: ${response.completionTimestamp}")
        }
        responses
    }

    suspend fun saveJobSurveyResponse(jobId: String, response: SurveyResponse?) {
        Log.d("AppDataStore", "Saving survey response for job $jobId")
        if (response != null) {
            Log.d("AppDataStore", "Survey response startTimestamp: ${response.startTimestamp}")
            Log.d("AppDataStore", "Survey response completionTimestamp: ${response.completionTimestamp}")
        }
        dataStore.edit { preferences ->
            val currentResponses = preferences[JOB_SURVEY_RESPONSES]?.let {
                Json.decodeFromString<Map<String, SurveyResponse>>(it)
            } ?: emptyMap()
            
            val updatedResponses = if (response != null) {
                currentResponses + (jobId to response)
            } else {
                currentResponses - jobId
            }
            
            preferences[JOB_SURVEY_RESPONSES] = Json.encodeToString(updatedResponses)
        }
    }

    // Job-specific history
    val jobHistory: Flow<Map<String, List<CounterChange>>> = dataStore.data.map { preferences ->
        val historyJson = preferences[JOB_HISTORY] ?: "{}"
        try {
            val history = Json.decodeFromString<Map<String, List<CounterChange>>>(historyJson)
            Log.d("AppDataStore", "=== Loading job history ===")
            Log.d("AppDataStore", "Loaded history for ${history.size} entries")
            history
        } catch (e: Exception) {
            Log.e("AppDataStore", "Error loading history", e)
            emptyMap()
        }
    }

    /**
     * Get history for a specific job, with pasture names as keys (without job prefix)
     */
    fun getJobHistory(jobId: String): Flow<Map<String, List<CounterChange>>> = dataStore.data.map { preferences ->
        val historyJson = preferences[JOB_HISTORY] ?: "{}"
        try {
            val allHistory = json.decodeFromString<Map<String, List<CounterChange>>>(historyJson)

            // Filter history for the specific job and remove job ID prefix
            val jobHistory = allHistory.filterKeys { historyKey ->
                extractJobIdFromHistoryKey(historyKey) == jobId
            }.mapKeys { (historyKey, _) ->
                extractPastureNameFromHistoryKey(historyKey) ?: historyKey
            }

            Log.d("AppDataStore", "Loaded history for job $jobId: ${jobHistory.size} pastures")
            jobHistory
        } catch (e: Exception) {
            Log.e("AppDataStore", "Error loading job history for $jobId", e)
            emptyMap()
        }
    }

    suspend fun saveJobHistory(jobId: String, history: Map<String, List<CounterChange>>) {
        Log.d("AppDataStore", "=== Saving job history ===")
        dataStore.edit { preferences ->
            val currentHistoryJson = preferences[JOB_HISTORY] ?: "{}"
            val currentHistory = try {
                json.decodeFromString<Map<String, List<CounterChange>>>(currentHistoryJson)
            } catch (e: Exception) {
                Log.e("AppDataStore", "Error decoding current history", e)
                emptyMap()
            }

            val updatedHistory = currentHistory + history.mapKeys { createHistoryKey(jobId, it.key) }
            val historyJson = json.encodeToString(updatedHistory)
            Log.d("AppDataStore", "Saving history for ${history.size} pastures")
            preferences[JOB_HISTORY] = historyJson
        }
    }

    /**
     * Delete history data for a specific pasture
     */
    suspend fun deletePastureHistory(jobId: String, pastureName: String) {
        Log.d("AppDataStore", "Deleting history for pasture: $pastureName in job: $jobId")
        dataStore.edit { preferences ->
            val currentHistoryJson = preferences[JOB_HISTORY] ?: "{}"
            val currentHistory = try {
                json.decodeFromString<Map<String, List<CounterChange>>>(currentHistoryJson)
            } catch (e: Exception) {
                Log.e("AppDataStore", "Error decoding history during pasture deletion", e)
                emptyMap()
            }

            val historyKeyToDelete = createHistoryKey(jobId, pastureName)
            val updatedHistory = currentHistory - historyKeyToDelete

            preferences[JOB_HISTORY] = json.encodeToString(updatedHistory)
            Log.d("AppDataStore", "Deleted history for key: $historyKeyToDelete")
        }
    }

    /**
     * Clean up old history entries, keeping only the most recent changes per pasture
     */
    suspend fun cleanupOldHistory() {
        Log.d("AppDataStore", "Cleaning up old history entries")
        dataStore.edit { preferences ->
            val currentHistory = preferences[JOB_HISTORY]?.let {
                try {
                    json.decodeFromString<Map<String, List<CounterChange>>>(it)
                } catch (e: Exception) {
                    Log.e("AppDataStore", "Error decoding history during cleanup", e)
                    emptyMap()
                }
            } ?: emptyMap()

            // Clean up history for each pasture, keeping only recent changes
            val cleanedHistory = currentHistory.mapValues { (historyKey, changes) ->
                val trimmedChanges = changes.takeLast(MAX_HISTORY_ITEMS_PER_JOB)
                Log.d("AppDataStore", "Cleaned history for $historyKey: ${changes.size} -> ${trimmedChanges.size} entries")
                trimmedChanges
            }

            preferences[JOB_HISTORY] = json.encodeToString(cleanedHistory)
            Log.d("AppDataStore", "History cleanup completed")
        }
    }

    // Job-specific survey states
    val jobSurveyStates: Flow<Map<String, Pair<Boolean, Boolean>>> = dataStore.data.map { preferences ->
        val statesJson = preferences[JOB_SURVEY_STATES] ?: "{}"
        Log.d("AppDataStore", "Loading job survey states JSON: $statesJson")
        try {
            val states = Json.decodeFromString<Map<String, List<Boolean>>>(statesJson)
                .mapValues { (_, state) -> state[0] to state[1] }
            Log.d("AppDataStore", "Loaded survey states for ${states.size} jobs")
            states
        } catch (e: Exception) {
            Log.e("AppDataStore", "Error decoding job survey states", e)
            emptyMap()
        }
    }

    suspend fun saveJobSurveyState(jobId: String, isStarted: Boolean, isExported: Boolean) {
        Log.d("AppDataStore", "Saving survey state for job $jobId - isStarted: $isStarted, isExported: $isExported")
        dataStore.edit { preferences ->
            try {
                val currentStates = preferences[JOB_SURVEY_STATES]?.let {
                    Json.decodeFromString<Map<String, List<Boolean>>>(it)
                } ?: emptyMap()
                
                val updatedStates = currentStates + (jobId to listOf(isStarted, isExported))
                val json = Json.encodeToString(updatedStates)
                Log.d("AppDataStore", "Saving job survey states JSON: $json")
                preferences[JOB_SURVEY_STATES] = json
            } catch (e: Exception) {
                Log.e("AppDataStore", "Error saving job survey state", e)
            }
        }
    }

    // Checklist completion per job
    val jobChecklistCompletion: Flow<Map<String, Set<String>>> = dataStore.data.map { preferences ->
        val json = preferences[JOB_CHECKLIST_COMPLETION] ?: "{}"
        try {
            Json.decodeFromString<Map<String, Set<String>>>(json)
        } catch (e: Exception) {
            emptyMap()
        }
    }

    suspend fun markChecklistCompleted(jobId: String, checklistType: String) {
        dataStore.edit { preferences ->
            val current = preferences[JOB_CHECKLIST_COMPLETION]?.let {
                try { Json.decodeFromString<Map<String, Set<String>>>(it) } catch (e: Exception) { emptyMap() }
            } ?: emptyMap()
            val updated = current + (jobId to ((current[jobId] ?: emptySet()) + checklistType))
            preferences[JOB_CHECKLIST_COMPLETION] = Json.encodeToString(updated)
        }
    }

    suspend fun clearChecklistCompletion(jobId: String) {
        dataStore.edit { preferences ->
            val current = preferences[JOB_CHECKLIST_COMPLETION]?.let {
                try { Json.decodeFromString<Map<String, Set<String>>>(it) } catch (e: Exception) { emptyMap() }
            } ?: emptyMap()
            val updated = current - jobId
            preferences[JOB_CHECKLIST_COMPLETION] = Json.encodeToString(updated)
        }
    }

    // Job-specific checklist data (client notes, etc.)
    val jobChecklistData: Flow<Map<String, ChecklistData>> = dataStore.data.map { preferences ->
        val json = preferences[JOB_CHECKLIST_DATA] ?: "{}"
        try {
            Json.decodeFromString<Map<String, ChecklistData>>(json)
        } catch (e: Exception) {
            emptyMap()
        }
    }

    suspend fun saveJobChecklistData(jobId: String, checklistData: ChecklistData) {
        Log.d("AppDataStore", "Saving checklist data for job $jobId: $checklistData")
        dataStore.edit { preferences ->
            val current = preferences[JOB_CHECKLIST_DATA]?.let {
                try { Json.decodeFromString<Map<String, ChecklistData>>(it) } catch (e: Exception) { emptyMap() }
            } ?: emptyMap()
            val updated = current + (jobId to checklistData)
            preferences[JOB_CHECKLIST_DATA] = Json.encodeToString(updated)
        }
    }

    // Save showPostFlightSurvey state per job
    suspend fun saveShowPostFlightSurvey(jobId: String, show: Boolean) {
        dataStore.edit { preferences ->
            val current = preferences[JOB_SHOW_POST_FLIGHT_SURVEY]?.let {
                try { Json.decodeFromString<Map<String, Boolean>>(it) } catch (e: Exception) { emptyMap() }
            } ?: emptyMap()
            val updated = current + (jobId to show)
            preferences[JOB_SHOW_POST_FLIGHT_SURVEY] = Json.encodeToString(updated)
        }
    }

    // Get showPostFlightSurvey state per job
    val showPostFlightSurvey: Flow<Map<String, Boolean>> = dataStore.data.map { preferences ->
        val json = preferences[JOB_SHOW_POST_FLIGHT_SURVEY] ?: "{}"
        try {
            Json.decodeFromString<Map<String, Boolean>>(json)
        } catch (e: Exception) {
            emptyMap()
        }
    }

    /**
     * Clear all job-specific data including pastures, history, and survey responses
     */
    suspend fun clearJobData(jobId: String) {
        Log.d("AppDataStore", "Clearing all data for job: $jobId")
        dataStore.edit { preferences ->
            // Clear pastures
            val currentPastures = preferences[JOB_PASTURES]?.let {
                try {
                    json.decodeFromString<Map<String, List<Pasture>>>(it)
                } catch (e: Exception) {
                    Log.e("AppDataStore", "Error decoding pastures during job data clear", e)
                    emptyMap()
                }
            } ?: emptyMap()
            preferences[JOB_PASTURES] = json.encodeToString(currentPastures - jobId)

            // Clear survey response
            val currentResponses = preferences[JOB_SURVEY_RESPONSES]?.let {
                try {
                    json.decodeFromString<Map<String, SurveyResponse>>(it)
                } catch (e: Exception) {
                    Log.e("AppDataStore", "Error decoding survey responses during job data clear", e)
                    emptyMap()
                }
            } ?: emptyMap()
            preferences[JOB_SURVEY_RESPONSES] = json.encodeToString(currentResponses - jobId)

            // Clear all history entries for this job
            val currentHistory = preferences[JOB_HISTORY]?.let {
                try {
                    json.decodeFromString<Map<String, List<CounterChange>>>(it)
                } catch (e: Exception) {
                    Log.e("AppDataStore", "Error decoding history during job data clear", e)
                    emptyMap()
                }
            } ?: emptyMap()

            // Filter out all history entries that belong to this job
            val filteredHistory = currentHistory.filterKeys { historyKey ->
                extractJobIdFromHistoryKey(historyKey) != jobId
            }
            preferences[JOB_HISTORY] = json.encodeToString(filteredHistory)
            
            // Clear survey state
            val currentStates = preferences[JOB_SURVEY_STATES]?.let {
                Json.decodeFromString<Map<String, List<Boolean>>>(it)
            } ?: emptyMap()
            preferences[JOB_SURVEY_STATES] = Json.encodeToString(currentStates - jobId)

            // Clear checklist completion
            val currentChecklistCompletion = preferences[JOB_CHECKLIST_COMPLETION]?.let {
                try { Json.decodeFromString<Map<String, Set<String>>>(it) } catch (e: Exception) { emptyMap() }
            } ?: emptyMap()
            preferences[JOB_CHECKLIST_COMPLETION] = Json.encodeToString(currentChecklistCompletion - jobId)

            // Clear checklist data
            val currentChecklistData = preferences[JOB_CHECKLIST_DATA]?.let {
                try { Json.decodeFromString<Map<String, ChecklistData>>(it) } catch (e: Exception) { emptyMap() }
            } ?: emptyMap()
            preferences[JOB_CHECKLIST_DATA] = Json.encodeToString(currentChecklistData - jobId)

            // Clear showPostFlightSurvey state
            val currentShowPostFlightSurvey = preferences[JOB_SHOW_POST_FLIGHT_SURVEY]?.let {
                try { Json.decodeFromString<Map<String, Boolean>>(it) } catch (e: Exception) { emptyMap() }
            } ?: emptyMap()
            preferences[JOB_SHOW_POST_FLIGHT_SURVEY] = Json.encodeToString(currentShowPostFlightSurvey - jobId)
        }
    }

    // Clear all data
    suspend fun clearAllData() {
        dataStore.edit { preferences ->
            preferences.clear()
        }
    }

    private fun logRetrievedJobs(jobs: List<Job>) {
        Log.d("AppDataStore", "Retrieved jobs: ${jobs.map { it.id to it.name }}")
    }
} 