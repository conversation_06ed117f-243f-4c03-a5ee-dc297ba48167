package com.example.finalapp.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.finalapp.data.AppDataStore
import com.example.finalapp.model.Pasture
import com.example.finalapp.util.ValidationUtils
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * ViewModel responsible for pasture management
 */
class PastureViewModel(application: Application) : AndroidViewModel(application) {
    private val dataStore = AppDataStore(application)
    private val pastureUpdateMutex = Mutex()
    
    // Store pastures per job ID
    private val _jobPastures = MutableStateFlow<Map<String, List<Pasture>>>(emptyMap())
    val jobPastures: StateFlow<Map<String, List<Pasture>>> = _jobPastures.asStateFlow()
    
    // Selected job ID
    private val _selectedJobId = MutableStateFlow<String?>(null)
    val selectedJobId: StateFlow<String?> = _selectedJobId.asStateFlow()
    
    // Selected pasture index
    private val _selectedPastureIndex = MutableStateFlow<Int?>(null)
    val selectedPastureIndex: StateFlow<Int?> = _selectedPastureIndex.asStateFlow()
    
    // Get pastures for current job
    val pastures: StateFlow<List<Pasture>> = combine(_selectedJobId, _jobPastures) { jobId, pastures ->
        jobId?.let { pastures[it] } ?: emptyList()
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    // Get selected pasture
    val selectedPasture: StateFlow<Pasture?> = combine(pastures, _selectedPastureIndex) { pastureList, index ->
        index?.let { pastureList.getOrNull(it) }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = null
    )
    
    init {
        // Load pastures from DataStore
        viewModelScope.launch {
            dataStore.jobPastures.collect { jobPastures ->
                _jobPastures.value = jobPastures
            }
        }
        
        // Load selected pasture index
        viewModelScope.launch {
            dataStore.selectedPastureIndex.collect { index ->
                _selectedPastureIndex.value = index
            }
        }
    }
    
    /**
     * Set the selected job ID
     */
    fun setSelectedJobId(jobId: String?) {
        _selectedJobId.value = jobId
        // Reset selected pasture index when job changes
        if (jobId != null) {
            val currentPastures = _jobPastures.value[jobId] ?: emptyList()
            if (currentPastures.isNotEmpty() && _selectedPastureIndex.value == null) {
                _selectedPastureIndex.value = 0
            } else if (currentPastures.isEmpty()) {
                _selectedPastureIndex.value = null
            }
        }
    }
    
    /**
     * Add a new pasture to the current job
     */
    suspend fun addPasture(name: String, temperature: Double): Result<Unit> {
        val jobId = _selectedJobId.value ?: return Result.failure(Exception("No job selected"))
        val currentPastures = _jobPastures.value[jobId] ?: emptyList()
        
        // Validate pasture name
        val validationResult = ValidationUtils.validatePastureName(
            name, 
            currentPastures.map { it.name }
        )
        if (!validationResult.isValid) {
            return Result.failure(Exception(validationResult.errorMessage))
        }
        
        // Validate temperature
        val tempValidationResult = ValidationUtils.validateTemperature(temperature.toString())
        if (!tempValidationResult.isValid) {
            return Result.failure(Exception(tempValidationResult.errorMessage))
        }
        
        return try {
            pastureUpdateMutex.withLock {
                val newPasture = Pasture(name = name.trim(), temperature = temperature)
                _jobPastures.update { currentMap ->
                    currentMap + (jobId to (currentPastures + newPasture))
                }
                
                // Set selected pasture index if this is the first pasture
                if (_selectedPastureIndex.value == null) {
                    _selectedPastureIndex.value = 0
                    dataStore.saveSelectedPastureIndex(0)
                }
                
                dataStore.saveJobPastures(_jobPastures.value)
                Log.d("PastureViewModel", "Added pasture: $name")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("PastureViewModel", "Error adding pasture", e)
            Result.failure(e)
        }
    }
    
    /**
     * Delete a pasture by index and clean up associated history data
     */
    suspend fun deletePasture(index: Int): Result<Unit> {
        val jobId = _selectedJobId.value ?: return Result.failure(Exception("No job selected"))
        val currentPastures = _jobPastures.value[jobId] ?: emptyList()

        if (index !in currentPastures.indices) {
            return Result.failure(Exception("Invalid pasture index"))
        }

        return try {
            pastureUpdateMutex.withLock {
                val pastureToDelete = currentPastures[index]
                val pastureNameToDelete = pastureToDelete.name
                val updatedPastures = currentPastures.toMutableList().apply {
                    removeAt(index)
                }

                _jobPastures.update { currentMap ->
                    currentMap + (jobId to updatedPastures)
                }

                // Adjust selected pasture index
                adjustSelectedPastureIndex(index, updatedPastures.size)

                // Save updated pastures
                dataStore.saveJobPastures(_jobPastures.value)

                // Clean up history data for the deleted pasture
                dataStore.deletePastureHistory(jobId, pastureNameToDelete)

                Log.d("PastureViewModel", "Deleted pasture: $pastureNameToDelete and its history data")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("PastureViewModel", "Error deleting pasture", e)
            Result.failure(e)
        }
    }
    
    /**
     * Select a pasture by index
     */
    fun selectPasture(index: Int) {
        val currentPastures = pastures.value
        if (index in currentPastures.indices) {
            _selectedPastureIndex.value = index
            viewModelScope.launch {
                dataStore.saveSelectedPastureIndex(index)
            }
            Log.d("PastureViewModel", "Selected pasture at index: $index")
        }
    }
    
    /**
     * Get pasture names for the current job
     */
    fun getPastureNames(): List<String> {
        return pastures.value.map { it.name }
    }
    
    /**
     * Get pasture by name
     */
    fun getPastureByName(name: String): Pasture? {
        return pastures.value.find { it.name == name }
    }
    
    /**
     * Update pasture temperature
     */
    suspend fun updatePastureTemperature(index: Int, temperature: Double): Result<Unit> {
        val jobId = _selectedJobId.value ?: return Result.failure(Exception("No job selected"))
        val currentPastures = _jobPastures.value[jobId] ?: emptyList()

        if (index !in currentPastures.indices) {
            return Result.failure(Exception("Invalid pasture index"))
        }

        // Validate temperature
        val validationResult = ValidationUtils.validateTemperature(temperature.toString())
        if (!validationResult.isValid) {
            return Result.failure(Exception(validationResult.errorMessage))
        }

        return try {
            pastureUpdateMutex.withLock {
                val updatedPastures = currentPastures.toMutableList().apply {
                    this[index] = this[index].copy(temperature = temperature)
                }

                _jobPastures.update { currentMap ->
                    currentMap + (jobId to updatedPastures)
                }

                dataStore.saveJobPastures(_jobPastures.value)
                Log.d("PastureViewModel", "Updated pasture temperature at index $index to $temperature")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("PastureViewModel", "Error updating pasture temperature", e)
            Result.failure(e)
        }
    }

    /**
     * Update pasture with new counters (used by CounterManagementViewModel)
     */
    suspend fun updatePastureCounters(pastureIndex: Int, updatedPasture: Pasture): Result<Unit> {
        val jobId = _selectedJobId.value ?: return Result.failure(Exception("No job selected"))
        val currentPastures = _jobPastures.value[jobId] ?: emptyList()

        if (pastureIndex !in currentPastures.indices) {
            return Result.failure(Exception("Invalid pasture index"))
        }

        return try {
            pastureUpdateMutex.withLock {
                val updatedPastures = currentPastures.toMutableList().apply {
                    this[pastureIndex] = updatedPasture
                }

                _jobPastures.update { currentMap ->
                    currentMap + (jobId to updatedPastures)
                }

                dataStore.saveJobPastures(_jobPastures.value)
                Log.d("PastureViewModel", "Updated pasture counters at index $pastureIndex")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("PastureViewModel", "Error updating pasture counters", e)
            Result.failure(e)
        }
    }
    
    /**
     * Clear all pastures for a job
     */
    suspend fun clearJobPastures(jobId: String) {
        try {
            pastureUpdateMutex.withLock {
                _jobPastures.update { currentMap ->
                    currentMap - jobId
                }
                
                // Reset selected pasture index if clearing current job
                if (_selectedJobId.value == jobId) {
                    _selectedPastureIndex.value = null
                    dataStore.saveSelectedPastureIndex(-1)
                }
                
                dataStore.saveJobPastures(_jobPastures.value)
                Log.d("PastureViewModel", "Cleared pastures for job: $jobId")
            }
        } catch (e: Exception) {
            Log.e("PastureViewModel", "Error clearing job pastures", e)
        }
    }
    
    /**
     * Adjust selected pasture index after deletion
     */
    private suspend fun adjustSelectedPastureIndex(deletedIndex: Int, newSize: Int) {
        val currentSelectedIndex = _selectedPastureIndex.value
        
        when {
            newSize == 0 -> {
                _selectedPastureIndex.value = null
                dataStore.saveSelectedPastureIndex(-1)
            }
            currentSelectedIndex == deletedIndex -> {
                // If we deleted the selected pasture, select the previous one or first one
                val newIndex = if (deletedIndex > 0) deletedIndex - 1 else 0
                _selectedPastureIndex.value = newIndex
                dataStore.saveSelectedPastureIndex(newIndex)
            }
            currentSelectedIndex != null && currentSelectedIndex > deletedIndex -> {
                // If we deleted a pasture before the selected one, adjust the index
                val newIndex = currentSelectedIndex - 1
                _selectedPastureIndex.value = newIndex
                dataStore.saveSelectedPastureIndex(newIndex)
            }
        }
    }
}
