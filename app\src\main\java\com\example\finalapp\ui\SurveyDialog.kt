package com.example.finalapp.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import com.example.finalapp.model.SurveyResponse
import android.util.Log

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SurveyDialog(
    onDismiss: () -> Unit,
    onConfirm: (SurveyResponse) -> Unit,
    startTimestamp: Long = System.currentTimeMillis()
) {
    var topography by remember { mutableStateOf("") }
    var cover by remember { mutableStateOf("") }
    var animalDensity by remember { mutableStateOf("") }
    var dayWeather by remember { mutableStateOf("") }
    var missionTypes by remember { mutableStateOf(listOf<String>()) }
    var takeOffsAndLandings by remember { mutableStateOf("0") }
    var additionalNotes by remember { mutableStateOf("") }
    var isNight by remember { mutableStateOf(false) }

    val scrollState = rememberScrollState()

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Survey Form") },
        text = {
            Column(
                modifier = Modifier
                    .verticalScroll(scrollState)
                    .padding(vertical = 8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Topography
                Text("Topography", style = MaterialTheme.typography.titleMedium)
                Column(Modifier.selectableGroup()) {
                    listOf("Extreme", "Rough", "Mild", "Light", "Flat").forEach { option ->
                        Row(
                            Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = (topography == option),
                                    onClick = { topography = option },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = (topography == option),
                                onClick = null
                            )
                            Text(
                                text = option,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }

                // Cover
                Text("Cover", style = MaterialTheme.typography.titleMedium)
                Column(Modifier.selectableGroup()) {
                    listOf("Very Heavy", "Heavy", "Medium", "Light", "Very Light").forEach { option ->
                        Row(
                            Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = (cover == option),
                                    onClick = { cover = option },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = (cover == option),
                                onClick = null
                            )
                            Text(
                                text = option,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }

                // Animal Density
                Text("Animal Density", style = MaterialTheme.typography.titleMedium)
                Column(Modifier.selectableGroup()) {
                    listOf("Very Dense", "Dense", "Average", "Thin", "Very Thin").forEach { option ->
                        Row(
                            Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = (animalDensity == option),
                                    onClick = { animalDensity = option },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = (animalDensity == option),
                                onClick = null
                            )
                            Text(
                                text = option,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }

                // Day Weather
                Text("Day Weather", style = MaterialTheme.typography.titleMedium)
                Column(Modifier.selectableGroup()) {
                    listOf("Clear", "Mild", "Storming").forEach { option ->
                        Row(
                            Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = (dayWeather == option),
                                    onClick = { dayWeather = option },
                                    role = Role.RadioButton
                                )
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = (dayWeather == option),
                                onClick = null
                            )
                            Text(
                                text = option,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }

                // Day/Night Selection
                Text("Survey Time", style = MaterialTheme.typography.titleMedium)
                Column(Modifier.selectableGroup()) {
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = !isNight,
                                onClick = { isNight = false },
                                role = Role.RadioButton
                            )
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = !isNight,
                            onClick = null
                        )
                        Text(
                            text = "Day",
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .selectable(
                                selected = isNight,
                                onClick = { isNight = true },
                                role = Role.RadioButton
                            )
                            .padding(vertical = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = isNight,
                            onClick = null
                        )
                        Text(
                            text = "Night",
                            modifier = Modifier.padding(start = 8.dp)
                        )
                    }
                }

                // Mission Type
                Text("Mission Type", style = MaterialTheme.typography.titleMedium)
                Column {
                    listOf("Survey", "Visible Camera Add-On", "Game Map Add-On").forEach { option ->
                        Row(
                            Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = missionTypes.contains(option),
                                onCheckedChange = { checked ->
                                    missionTypes = if (checked) {
                                        missionTypes + option
                                    } else {
                                        missionTypes - option
                                    }
                                }
                            )
                            Text(
                                text = option,
                                modifier = Modifier.padding(start = 8.dp)
                            )
                        }
                    }
                }

                // Take Offs and Landings
                Text("Number of Take Offs and Landings", style = MaterialTheme.typography.titleMedium)
                TextField(
                    value = takeOffsAndLandings,
                    onValueChange = { takeOffsAndLandings = it },
                    keyboardOptions = androidx.compose.foundation.text.KeyboardOptions(
                        keyboardType = androidx.compose.ui.text.input.KeyboardType.Number
                    ),
                    modifier = Modifier.fillMaxWidth()
                )

                // Additional Notes
                Text("Additional Notes About Survey", style = MaterialTheme.typography.titleMedium)
                TextField(
                    value = additionalNotes,
                    onValueChange = { additionalNotes = it },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 3
                )


            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val completionTimestamp = System.currentTimeMillis()
                    
                    // Add logging for survey dialog completion
                    Log.d("SurveyDialog", "Survey dialog completion:")
                    Log.d("SurveyDialog", "startTimestamp: $startTimestamp")
                    Log.d("SurveyDialog", "completionTimestamp: $completionTimestamp")
                    Log.d("SurveyDialog", "Duration: ${completionTimestamp - startTimestamp} ms")
                    
                    onConfirm(
                        SurveyResponse(
                            topography = topography,
                            cover = cover,
                            animalDensity = animalDensity,
                            dayWeather = dayWeather,
                            missionTypes = missionTypes,
                            takeOffsAndLandings = takeOffsAndLandings.toIntOrNull() ?: 0,
                            additionalNotes = additionalNotes,
                            startTimestamp = startTimestamp, // preserve the passed-in startTimestamp
                            completionTimestamp = completionTimestamp,
                            isNight = isNight
                        )
                    )
                }
            ) {
                Text("Submit")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
} 