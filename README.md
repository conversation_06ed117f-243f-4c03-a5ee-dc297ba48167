# Sky Senderos - Livestock Survey Application

A comprehensive Android application for conducting livestock surveys using drones. The app enables pilots to manage jobs, count animals in different pastures, and export data to Google Sheets.

## 🚀 Features

### Core Functionality
- **Job Management**: Create and manage survey jobs with detailed information
- **Pasture Management**: Add, edit, and delete pastures with temperature tracking
- **Counter System**: Flexible counter system for different animal types and categories
- **History Tracking**: Complete audit trail of all counter changes with undo functionality
- **Data Export**: Seamless integration with Google Sheets for data export
- **Checklist System**: Pre-flight, post-flight, and equipment setup checklists

### User Experience
- **Validation**: Comprehensive input validation with user-friendly error messages
- **Confirmation Dialogs**: Safety confirmations for destructive actions
- **Loading States**: Visual feedback for all async operations
- **Error Handling**: Robust error handling with recovery suggestions
- **Accessibility**: Content descriptions and semantic properties for screen readers

### Data Management
- **Offline Support**: Local data storage with DataStore
- **Data Persistence**: Automatic saving of all user data
- **Data Integrity**: Validation and error recovery mechanisms
- **Export Integration**: Direct export to Google Sheets with proper formatting

## 🏗️ Architecture

### MVVM Pattern
The application follows the Model-View-ViewModel (MVVM) architectural pattern:

- **Models**: Data classes representing core entities (Job, Pasture, Counter, etc.)
- **ViewModels**: Business logic and state management
- **Views**: Jetpack Compose UI components
- **Repository Pattern**: Data access abstraction through DataStore

### Key Components

#### ViewModels
- `PastureViewModel`: Manages pasture operations (add, delete, select, update)
- `CounterManagementViewModel`: Handles counter operations and history tracking
- `JobViewModel`: Manages job lifecycle and pilot information
- `CounterViewModel`: Legacy ViewModel (being phased out in favor of specialized ViewModels)

#### Data Layer
- `AppDataStore`: Centralized data persistence using Android DataStore
- `GoogleSheetsService`: Integration with Google Sheets API
- `ApiService`: External API communication

#### UI Components
- `CommonDialogs`: Reusable dialog components with validation
- `CounterScreen`: Main survey interface
- `ChecklistDialogs`: Pre/post-flight checklist components

#### Utilities
- `ValidationUtils`: Input validation with comprehensive error messages
- `ErrorHandler`: Centralized error handling and user-friendly messaging
- `LoadingStateManager`: Loading state management for async operations
- `Constants`: Application-wide constants and configuration

### Data Flow
1. User interactions trigger ViewModel methods
2. ViewModels validate input and update state
3. State changes are observed by UI components
4. Data is persisted through AppDataStore
5. Export operations communicate with Google Sheets API

## 🛠️ Setup Instructions

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 24 (API level 24) or higher
- Google Cloud Console project with Sheets API enabled
- Service account credentials for Google Sheets integration

### Installation

1. **Clone the Repository**
   ```bash
   git clone https://github.com/IkeSalinsky/Skyapp.git
   cd Skyapp
   ```

2. **Configure Google Sheets Integration**
   - Create a Google Cloud Console project
   - Enable the Google Sheets API
   - Create a service account and download the JSON credentials
   - Place the credentials file in `app/src/main/res/raw/` as `skysenderos_459013_8c4a24330608.json`

3. **Build and Run**
   ```bash
   ./gradlew assembleDebug
   ```
   Or use Android Studio's build system.

### Configuration

#### API Configuration
Update the API configuration in `AddJobScreen.kt`:
```kotlin
private const val API_URL = "your-api-endpoint"
private const val API_KEY = "your-api-key"
```

#### Google Sheets Configuration
Ensure your service account has access to the target Google Sheets and update the application name in `GoogleSheetsService.kt` if needed.

## 📱 Usage Guide

### Starting a Survey

1. **Launch Application**: Open the Sky Senderos app
2. **Create/Select Job**: Choose an existing job or create a new one from survey data
3. **Enter Pilot Information**: Provide pilot name and drone details
4. **Complete Checklists**: Follow the pre-flight checklist procedures

### Managing Pastures

1. **Add Pasture**: Use the "+" button next to the pasture dropdown
   - Enter unique pasture name (validation prevents duplicates)
   - Set temperature in Fahrenheit
2. **Select Pasture**: Use the dropdown to switch between pastures
3. **Delete Pasture**: Click the delete icon in the dropdown (requires confirmation)

### Counting Animals

1. **Add Counters**: Click "Add Counter" to create new animal counters
2. **Increment/Decrement**: Tap counter buttons to adjust counts
3. **Custom Amounts**: Long-press for custom increment/decrement amounts
4. **View History**: Access the history dialog to see all changes and undo if needed

### Completing Survey

1. **Finish Survey**: Click "Finish Survey" button (requires confirmation)
2. **Post-Flight Checklist**: Complete the post-flight procedures
3. **Export Data**: Data is automatically exported to Google Sheets
4. **Review Results**: Verify exported data in the connected Google Sheet

## 🔧 Development

### Code Style
- Follow Kotlin coding conventions
- Use meaningful variable and function names
- Add KDoc comments for public APIs
- Maintain consistent indentation (4 spaces)

### Testing
```bash
# Run unit tests
./gradlew test

# Run instrumented tests
./gradlew connectedAndroidTest
```

### Building
```bash
# Debug build
./gradlew assembleDebug

# Release build
./gradlew assembleRelease
```

## 📊 Data Models

### Core Entities

#### Job
```kotlin
data class Job(
    val id: String,
    val name: String,
    val status: String,
    val coordinates: String,
    val gateCode: String,
    val pilotInfo: PilotInfo?,
    val googleSheetsId: String?,
    // ... other properties
)
```

#### Pasture
```kotlin
data class Pasture(
    val id: String,
    val name: String,
    val temperature: Double,
    val counters: List<Counter>,
    val history: List<CounterChange>
)
```

#### Counter
```kotlin
data class Counter(
    val name: String,
    val value: Int
)
```

## 🔐 Security

- Service account credentials are stored securely in app resources
- API keys should be stored in environment variables or secure configuration
- User data is stored locally using encrypted DataStore
- Network communications use HTTPS

## 🐛 Troubleshooting

### Common Issues

1. **Google Sheets Export Fails**
   - Verify service account credentials
   - Check internet connectivity
   - Ensure Sheets API is enabled

2. **Data Not Persisting**
   - Check device storage space
   - Verify app permissions
   - Clear app data and restart

3. **Validation Errors**
   - Check input format requirements
   - Ensure unique names for pastures/counters
   - Verify temperature ranges

### Debug Mode
Enable debug logging by setting log level in `Constants.kt`:
```kotlin
const val DEBUG_MODE = true
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the troubleshooting section above

## 🔄 Version History

See [CHANGELOG.md](CHANGELOG.md) for detailed version history and changes.
