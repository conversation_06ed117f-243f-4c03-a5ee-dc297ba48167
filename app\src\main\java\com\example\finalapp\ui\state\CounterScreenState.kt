package com.example.finalapp.ui.state

import androidx.compose.runtime.Stable
import com.example.finalapp.model.Job
import com.example.finalapp.model.Pasture

/**
 * UI state for the Counter Screen
 */
@Stable
data class CounterScreenState(
    // Data state
    val pastures: List<Pasture> = emptyList(),
    val selectedPastureIndex: Int? = null,
    val selectedJob: Job? = null,
    val isSurveyStarted: Boolean = false,
    val isExported: Boolean = false,
    val jobsWithCompletedSurveys: Set<String> = emptySet(),
    
    // Dialog states
    val showAddPastureDialog: Boolean = false,
    val showAddCounterDialog: Boolean = false,
    val showHistoryDialog: Boolean = false,
    val showJobDetailsDialog: Boolean = false,
    val showFinishSurveyConfirmation: Boolean = false,
    val showCancelConfirmation: Boolean = false,
    val showDeletePastureConfirmation: Boolean = false,
    val showDeleteCounterConfirmation: Boolean = false,
    
    // Checklist dialog states
    val showPreDriveChecklist: Boolean = false,
    val showPreFlightChecklist: Boolean = false,
    val showEquipmentSetupChecklist: Boolean = false,
    val showPostFlightChecklist: Boolean = false,
    val showPostJobChecklist: Boolean = false,
    
    // Loading states
    val isLoading: Boolean = false,
    val isSaving: Boolean = false,
    val isExporting: Boolean = false,
    
    // Error states
    val errorMessage: String? = null,
    val showErrorDialog: Boolean = false,
    
    // Success states
    val successMessage: String? = null,
    val showSuccessDialog: Boolean = false,
    
    // Delete confirmation states
    val pastureToDelete: Int? = null,
    val pastureNameToDelete: String = "",
    val counterToDelete: Int? = null,
    val counterNameToDelete: String = "",
    
    // Other UI states
    val showFullScreenImage: Boolean = false,
    val showPostFlightSurvey: Boolean = false,
    val checklistCompletion: Set<String> = emptySet(),

    // Export retry dialog states
    val showExportRetryDialog: Boolean = false,
    val exportRetryJobId: String? = null,
    val exportRetryNewSheetsId: String = ""
) {
    
    /**
     * Computed properties for derived state
     */
    val selectedPasture: Pasture? 
        get() = selectedPastureIndex?.let { index ->
            pastures.getOrNull(index)
        }
    
    val hasSelectedPasture: Boolean
        get() = selectedPastureIndex != null && selectedPastureIndex in pastures.indices
    
    val canAddCounters: Boolean
        get() = hasSelectedPasture && isSurveyStarted
    
    val canFinishSurvey: Boolean
        get() = pastures.isNotEmpty() && isSurveyStarted
    
    val isPreDriveCompleted: Boolean
        get() = checklistCompletion.contains("pre_drive")
    
    val isPreFlightCompleted: Boolean
        get() = checklistCompletion.contains("pre_flight")
    
    val isEquipmentSetupCompleted: Boolean
        get() = checklistCompletion.contains("equipment_setup")
    
    val isPostFlightCompleted: Boolean
        get() = checklistCompletion.contains("post_flight")
    
    val isPostJobCompleted: Boolean
        get() = checklistCompletion.contains("post_job")
    
    val existingPastureNames: List<String>
        get() = pastures.map { it.name }
    
    val existingCounterNames: List<String>
        get() = selectedPasture?.counters?.map { it.name } ?: emptyList()
    
    /**
     * Helper functions for state updates
     */
    fun showDialog(dialogType: DialogType): CounterScreenState {
        return when (dialogType) {
            DialogType.ADD_PASTURE -> copy(showAddPastureDialog = true)
            DialogType.ADD_COUNTER -> copy(showAddCounterDialog = true)
            DialogType.HISTORY -> copy(showHistoryDialog = true)
            DialogType.JOB_DETAILS -> copy(showJobDetailsDialog = true)
            DialogType.FINISH_SURVEY_CONFIRMATION -> copy(showFinishSurveyConfirmation = true)
            DialogType.CANCEL_CONFIRMATION -> copy(showCancelConfirmation = true)
            DialogType.DELETE_PASTURE_CONFIRMATION -> copy(showDeletePastureConfirmation = true)
            DialogType.DELETE_COUNTER_CONFIRMATION -> copy(showDeleteCounterConfirmation = true)
            DialogType.ERROR -> copy(showErrorDialog = true)
            DialogType.SUCCESS -> copy(showSuccessDialog = true)
        }
    }
    
    fun hideDialog(dialogType: DialogType): CounterScreenState {
        return when (dialogType) {
            DialogType.ADD_PASTURE -> copy(showAddPastureDialog = false)
            DialogType.ADD_COUNTER -> copy(showAddCounterDialog = false)
            DialogType.HISTORY -> copy(showHistoryDialog = false)
            DialogType.JOB_DETAILS -> copy(showJobDetailsDialog = false)
            DialogType.FINISH_SURVEY_CONFIRMATION -> copy(showFinishSurveyConfirmation = false)
            DialogType.CANCEL_CONFIRMATION -> copy(showCancelConfirmation = false)
            DialogType.DELETE_PASTURE_CONFIRMATION -> copy(
                showDeletePastureConfirmation = false,
                pastureToDelete = null,
                pastureNameToDelete = ""
            )
            DialogType.DELETE_COUNTER_CONFIRMATION -> copy(
                showDeleteCounterConfirmation = false,
                counterToDelete = null,
                counterNameToDelete = ""
            )
            DialogType.ERROR -> copy(showErrorDialog = false, errorMessage = null)
            DialogType.SUCCESS -> copy(showSuccessDialog = false, successMessage = null)
        }
    }
    
    fun showChecklist(checklistType: ChecklistType): CounterScreenState {
        return when (checklistType) {
            ChecklistType.PRE_DRIVE -> copy(showPreDriveChecklist = true)
            ChecklistType.PRE_FLIGHT -> copy(showPreFlightChecklist = true)
            ChecklistType.EQUIPMENT_SETUP -> copy(showEquipmentSetupChecklist = true)
            ChecklistType.POST_FLIGHT -> copy(showPostFlightChecklist = true)
            ChecklistType.POST_JOB -> copy(showPostJobChecklist = true)
        }
    }
    
    fun hideChecklist(checklistType: ChecklistType): CounterScreenState {
        return when (checklistType) {
            ChecklistType.PRE_DRIVE -> copy(showPreDriveChecklist = false)
            ChecklistType.PRE_FLIGHT -> copy(showPreFlightChecklist = false)
            ChecklistType.EQUIPMENT_SETUP -> copy(showEquipmentSetupChecklist = false)
            ChecklistType.POST_FLIGHT -> copy(showPostFlightChecklist = false)
            ChecklistType.POST_JOB -> copy(showPostJobChecklist = false)
        }
    }
    
    fun setLoading(isLoading: Boolean): CounterScreenState {
        return copy(isLoading = isLoading)
    }
    
    fun setSaving(isSaving: Boolean): CounterScreenState {
        return copy(isSaving = isSaving)
    }
    
    fun setExporting(isExporting: Boolean): CounterScreenState {
        return copy(isExporting = isExporting)
    }
    
    fun setError(message: String): CounterScreenState {
        return copy(errorMessage = message, showErrorDialog = true)
    }
    
    fun setSuccess(message: String): CounterScreenState {
        return copy(successMessage = message, showSuccessDialog = true)
    }
    
    fun preparePastureDelete(index: Int, name: String): CounterScreenState {
        return copy(
            pastureToDelete = index,
            pastureNameToDelete = name,
            showDeletePastureConfirmation = true
        )
    }
    
    fun prepareCounterDelete(index: Int, name: String): CounterScreenState {
        return copy(
            counterToDelete = index,
            counterNameToDelete = name,
            showDeleteCounterConfirmation = true
        )
    }

    fun showExportRetryDialog(jobId: String): CounterScreenState {
        return copy(
            showExportRetryDialog = true,
            exportRetryJobId = jobId,
            exportRetryNewSheetsId = ""
        )
    }

    fun hideExportRetryDialog(): CounterScreenState {
        return copy(
            showExportRetryDialog = false,
            exportRetryJobId = null,
            exportRetryNewSheetsId = ""
        )
    }

    fun updateExportRetryNewSheetsId(newId: String): CounterScreenState {
        return copy(exportRetryNewSheetsId = newId)
    }
}

/**
 * Enum for dialog types
 */
enum class DialogType {
    ADD_PASTURE,
    ADD_COUNTER,
    HISTORY,
    JOB_DETAILS,
    FINISH_SURVEY_CONFIRMATION,
    CANCEL_CONFIRMATION,
    DELETE_PASTURE_CONFIRMATION,
    DELETE_COUNTER_CONFIRMATION,
    ERROR,
    SUCCESS
}

/**
 * Enum for checklist types
 */
enum class ChecklistType {
    PRE_DRIVE,
    PRE_FLIGHT,
    EQUIPMENT_SETUP,
    POST_FLIGHT,
    POST_JOB
}
