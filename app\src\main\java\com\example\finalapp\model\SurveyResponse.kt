package com.example.finalapp.model

import kotlinx.serialization.Serializable

@Serializable
data class SurveyResponse(
    val topography: String,
    val cover: String,
    val animalDensity: String,
    val dayWeather: String,
    val missionTypes: List<String>,
    val takeOffsAndLandings: Int,
    val additionalNotes: String,
    val remarksProceduresManeuvers: String = "",
    val batteryChanges: Int = 0,
    val startTimestamp: Long,
    val completionTimestamp: Long,
    val isNight: Boolean = false
)