package com.example.finalapp.util

import com.example.finalapp.util.Constants.Temperature
import com.example.finalapp.util.Constants.ValidationMessages

/**
 * Utility functions for input validation
 */
object ValidationUtils {
    
    /**
     * Validation result containing success status and error message
     */
    data class ValidationResult(
        val isValid: <PERSON><PERSON><PERSON>,
        val errorMessage: String? = null
    ) {
        companion object {
            fun success() = ValidationResult(true)
            fun error(message: String) = ValidationResult(false, message)
        }
    }
    
    /**
     * Validates pasture name
     */
    fun validatePastureName(
        name: String,
        existingNames: List<String> = emptyList()
    ): ValidationResult {
        return when {
            name.isBlank() -> ValidationResult.error(ValidationMessages.EMPTY_PASTURE_NAME)
            existingNames.any { it.equals(name.trim(), ignoreCase = true) } -> 
                ValidationResult.error(ValidationMessages.DUPLICATE_PASTURE_NAME)
            name.trim().length > 50 -> ValidationResult.error("Pasture name must be 50 characters or less")
            else -> ValidationResult.success()
        }
    }
    
    /**
     * Validates temperature input
     */
    fun validateTemperature(temperatureString: String): ValidationResult {
        return try {
            val temperature = temperatureString.toDouble()
            when {
                temperature < Temperature.MIN_TEMPERATURE || temperature > Temperature.MAX_TEMPERATURE ->
                    ValidationResult.error(ValidationMessages.TEMPERATURE_OUT_OF_RANGE)
                else -> ValidationResult.success()
            }
        } catch (e: NumberFormatException) {
            ValidationResult.error(ValidationMessages.INVALID_TEMPERATURE)
        }
    }
    
    /**
     * Validates counter name
     */
    fun validateCounterName(
        name: String,
        existingNames: List<String> = emptyList()
    ): ValidationResult {
        return when {
            name.isBlank() -> ValidationResult.error(ValidationMessages.EMPTY_COUNTER_NAME)
            existingNames.any { it.equals(name.trim(), ignoreCase = true) } -> 
                ValidationResult.error(ValidationMessages.DUPLICATE_COUNTER_NAME)
            name.trim().length > 30 -> ValidationResult.error("Counter name must be 30 characters or less")
            else -> ValidationResult.success()
        }
    }
    
    /**
     * Validates counter value
     */
    fun validateCounterValue(valueString: String): ValidationResult {
        return try {
            val value = valueString.toInt()
            when {
                value < Constants.Counter.MIN_VALUE -> 
                    ValidationResult.error("Value cannot be negative")
                value > Constants.Counter.MAX_VALUE -> 
                    ValidationResult.error("Value cannot exceed ${Constants.Counter.MAX_VALUE}")
                else -> ValidationResult.success()
            }
        } catch (e: NumberFormatException) {
            ValidationResult.error("Please enter a valid number")
        }
    }
    
    /**
     * Validates custom increment amount
     */
    fun validateCustomAmount(amountString: String): ValidationResult {
        return try {
            val amount = amountString.toInt()
            when {
                amount == 0 -> ValidationResult.error("Amount cannot be zero")
                amount < -1000 || amount > 1000 -> 
                    ValidationResult.error("Amount must be between -1000 and 1000")
                else -> ValidationResult.success()
            }
        } catch (e: NumberFormatException) {
            ValidationResult.error("Please enter a valid number")
        }
    }
    
    /**
     * Sanitizes input by trimming whitespace and removing special characters if needed
     */
    fun sanitizeInput(input: String): String {
        return input.trim()
    }
    
    /**
     * Checks if a string is a valid name (contains only letters, numbers, spaces, and basic punctuation)
     */
    fun isValidName(name: String): Boolean {
        val regex = Regex("^[a-zA-Z0-9\\s._-]+$")
        return name.matches(regex)
    }
    
    /**
     * Validates job name
     */
    fun validateJobName(name: String): ValidationResult {
        return when {
            name.isBlank() -> ValidationResult.error("Job name cannot be empty")
            name.trim().length > 100 -> ValidationResult.error("Job name must be 100 characters or less")
            !isValidName(name.trim()) -> ValidationResult.error("Job name contains invalid characters")
            else -> ValidationResult.success()
        }
    }
    
    /**
     * Validates email format
     */
    fun validateEmail(email: String): ValidationResult {
        val emailRegex = Regex("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")
        return when {
            email.isBlank() -> ValidationResult.error("Email cannot be empty")
            !email.matches(emailRegex) -> ValidationResult.error("Please enter a valid email address")
            else -> ValidationResult.success()
        }
    }
    
    /**
     * Validates that at least one pasture exists before finishing survey
     */
    fun validateSurveyCompletion(pastureCount: Int): ValidationResult {
        return when {
            pastureCount == 0 -> ValidationResult.error("At least one pasture is required to complete the survey")
            else -> ValidationResult.success()
        }
    }
    
    /**
     * Validates that required checklists are completed
     */
    fun validateChecklistCompletion(
        completedChecklists: Set<String>,
        requiredChecklists: Set<String>
    ): ValidationResult {
        val missingChecklists = requiredChecklists - completedChecklists
        return when {
            missingChecklists.isNotEmpty() -> 
                ValidationResult.error("Please complete all required checklists: ${missingChecklists.joinToString(", ")}")
            else -> ValidationResult.success()
        }
    }
}
