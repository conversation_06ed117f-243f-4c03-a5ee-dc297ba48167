package com.example.finalapp.ui

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.PictureAsPdf
import androidx.compose.material.icons.filled.Image
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.finalapp.model.Counter
import com.example.finalapp.model.CounterChange
import com.example.finalapp.model.Pasture
import com.example.finalapp.model.Job
import com.example.finalapp.viewmodel.CounterViewModel
import com.example.finalapp.viewmodel.JobViewModel
import com.example.finalapp.ui.components.ExportRetryDialog
import com.example.finalapp.ui.components.TwoFieldInputDialog
import com.example.finalapp.util.Constants.FieldLabels
import com.example.finalapp.util.ValidationUtils
import java.text.SimpleDateFormat
import java.util.*
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.draw.scale
import androidx.compose.animation.core.animateFloatAsState
import android.graphics.BitmapFactory
import android.util.Base64
import androidx.compose.ui.layout.ContentScale
import androidx.compose.foundation.Image
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.example.finalapp.ui.JobDetailsDialog
import android.util.Log
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.foundation.border
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.foundation.text.KeyboardOptions
import com.example.finalapp.ui.PreDriveChecklistDialog
import com.example.finalapp.ui.PreFlightChecklistDialog
import com.example.finalapp.ui.EquipmentSetupChecklistDialog
import com.example.finalapp.ui.PostFlightChecklistDialog
import com.example.finalapp.ui.PostJobChecklistDialog
import com.example.finalapp.ui.PdfViewerDialog

data class SpeciesCategory(val species: String, val categories: List<String>)

val speciesList = listOf(
    SpeciesCategory("Whitetail", listOf("Bucks", "Does", "Fawns", "Yearlings", "Spikes", "Mature Bucks", "Young Bucks", "Middle-Aged Bucks")),
    SpeciesCategory("Axis", listOf("Bucks", "Does", "Fawns", "Yearlings")),
    SpeciesCategory("Fallow", listOf("Bucks", "Does", "Fawns", "Yearlings")),
    SpeciesCategory("Standard Sika", listOf("Bucks", "Does", "Fawns")),
    SpeciesCategory("Manchurian Sika", listOf("Bucks", "Does", "Fawns")),
    SpeciesCategory("Red Deer", listOf("Stag", "Females", "Fawns")),
    SpeciesCategory("Elk", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Pere David", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Eld's Deer", listOf("Bucks", "Does", "Fawns")),
    SpeciesCategory("Muntjac Deer", listOf("Bucks", "Does", "Fawns")),
    SpeciesCategory("Hog Deer", listOf("Bucks", "Does", "Fawns")),
    SpeciesCategory("Barasingha", listOf("Bucks", "Does", "Fawns")),
    SpeciesCategory("Arabian Oryx", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Scimitar Oryx", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Gemsbok", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Sable", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Kudu", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Addax", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Eland", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Blesbok", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Bongo", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Red Lechwe", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Nile Lechwe", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Blackbuck", listOf("Bucks", "Does", "Fawns")),
    SpeciesCategory("Dama Gazelle", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Waterbuck", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Impala", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Springbok", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Thomson's Gazelle", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Grant's Gazelle", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Aoudad", listOf("Rams", "Ewes", "Lambs")),
    SpeciesCategory("Himalayan Tahr", listOf("Bucks", "Nannies", "Kids")),
    SpeciesCategory("Ibex", listOf("Bucks", "Nannies", "Kids")),
    SpeciesCategory("Jacob's Four Horn", listOf("Rams", "Ewes", "Lambs")),
    SpeciesCategory("Persian Ibex", listOf("Bucks", "Nannies", "Kids")),
    SpeciesCategory("Goat Mix", listOf("Bucks", "Nannies", "Kids")),
    SpeciesCategory("Transcaspian Urial", listOf("Rams", "Ewes", "Lambs")),
    SpeciesCategory("Texas Dall Sheep", listOf("Rams", "Ewes", "Lambs")),
    SpeciesCategory("Blue Sheep", listOf("Rams", "Ewes", "Lamb")),
    SpeciesCategory("Markhor", listOf("Bucks", "Nannies", "Kids")),
    SpeciesCategory("Stumberg", listOf("Rams", "Nannies", "Kids")),
    SpeciesCategory("Strawberry Urial", listOf("Rams", "Ewes", "Lambs")),
    SpeciesCategory("Mouflon Sheep", listOf("Rams", "Ewes", "Lambs")),
    SpeciesCategory("Snow Urial", listOf("Rams", "Ewes", "Lambs")),
    SpeciesCategory("Banteng", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Gaur", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Yak", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Nilgai", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Zebra", listOf("Stallions", "Mares", "Foals")),
    SpeciesCategory("American Bison", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Water Buffalo", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Gayal", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Watusi", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Zebu", listOf("Bulls", "Cows", "Calves")),
    SpeciesCategory("Black Wildebeest", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Blue Wildebeest", listOf("Males", "Females", "Calves")),
    SpeciesCategory("Feral Hog", emptyList()),
    SpeciesCategory("Javelina", emptyList()),
    SpeciesCategory("Coyotes", emptyList()),
    SpeciesCategory("Bobcats", emptyList()),
    SpeciesCategory("Grey Fox", emptyList()),
    SpeciesCategory("Red Fox", emptyList()),
    SpeciesCategory("Roan", listOf("Stallions", "Mares", "Foals")),
    SpeciesCategory("Red Kangaroo", emptyList()),
    SpeciesCategory("Emu", emptyList()),
    SpeciesCategory("Ostrich", emptyList()),
    SpeciesCategory("Rhea", emptyList()),
    SpeciesCategory("Turkey", emptyList())
)

val presetCounters = speciesList.flatMap { speciesCat ->
    if (speciesCat.categories.isEmpty()) listOf(speciesCat.species)
    else speciesCat.categories.map { "${speciesCat.species} - $it" }
}

// Helper function to capitalize words
private fun String.capitalizeWords(): String {
    return this.split(" ").joinToString(" ") { word ->
        word.replaceFirstChar { it.uppercase() }
    }
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun CounterScreen(
    onNavigateToSurvey: () -> Unit,
    onNavigateToPilotForm: () -> Unit,
    onNavigateToSurveys: () -> Unit,
    viewModel: CounterViewModel = viewModel(),
    jobViewModel: JobViewModel = viewModel()
) {
    val pastures by viewModel.pastures.collectAsState()
    val selectedPastureIndex by viewModel.selectedPastureIndex.collectAsState()
    val history by viewModel.history.collectAsState()
    val isSurveyStarted by viewModel.isSurveyStarted.collectAsState()
    val isExported by viewModel.isExported.collectAsState()
    val selectedJob by viewModel.selectedJob.collectAsState()
    val jobsWithCompletedSurveys by viewModel.jobsWithCompletedSurveys.collectAsState()
    var showAddPastureDialog by remember { mutableStateOf(false) }
    var showAddCounterDialog by remember { mutableStateOf(false) }
    var showHistoryDialog by remember { mutableStateOf(false) }
    var showJobDetailsDialog by remember { mutableStateOf(false) }
    val showPostFlightSurvey by viewModel.showPostFlightSurvey.collectAsState()
    var showFinishSurveyConfirmation by remember { mutableStateOf(false) }
    var showCancelConfirmation by remember { mutableStateOf(false) }
    var showDeletePastureConfirmation by remember { mutableStateOf(false) }
    var pastureToDelete by remember { mutableStateOf<Int?>(null) }
    var pastureNameToDelete by remember { mutableStateOf("") }
    var selectedPasture by remember { mutableStateOf<Pasture?>(null) }
    var showFullScreenImage by remember { mutableStateOf(false) }
    val pilotInfo = selectedJob?.let { jobViewModel.getPilotInfo(it.id) }
    val scope = rememberCoroutineScope()

    // Safety check: if job is exported, redirect to surveys page
    // This should not normally happen due to navigation logic, but provides a safety net
    LaunchedEffect(isExported) {
        if (isExported) {
            onNavigateToSurveys()
        }
    }

    // Checklist dialog states
    var showPreDriveChecklist by remember { mutableStateOf(false) }
    var showPreFlightChecklist by remember { mutableStateOf(false) }
    var showEquipmentSetupChecklist by remember { mutableStateOf(false) }
    var showPostFlightChecklist by remember { mutableStateOf(false) }
    var showPostJobChecklist by remember { mutableStateOf(false) }

    // Export retry dialog states
    var showExportRetryDialog by remember { mutableStateOf(false) }
    var exportRetryJobId by remember { mutableStateOf<String?>(null) }
    var exportRetryNewSheetsId by remember { mutableStateOf("") }

    // Checklist completion state
    val checklistCompletion by viewModel.checklistCompletion.collectAsState()
    val currentJobId = selectedJob?.id
    val isPreDriveCompleted = viewModel.isChecklistCompleted(currentJobId, "pre_drive")
    val isPreFlightCompleted = viewModel.isChecklistCompleted(currentJobId, "pre_flight")
    val isEquipmentSetupCompleted = viewModel.isChecklistCompleted(currentJobId, "equipment_setup")
    val isPostFlightCompleted = viewModel.isChecklistCompleted(currentJobId, "post_flight")
    val isPostJobCompleted = viewModel.isChecklistCompleted(currentJobId, "post_job")

    // Check if the selected job is ready for export
    val isReadyForExport = selectedJob?.let { job ->
        jobsWithCompletedSurveys.contains(job.id)
    } ?: false

    // Check if job is ready for survey (pre-drive completed)
    val isJobReadyForSurvey = isPreDriveCompleted

    // Check if job is ready to finish (all checklists except post-job completed)
    val isJobReadyToFinish = isPreDriveCompleted && isPreFlightCompleted && 
                             isEquipmentSetupCompleted && isPostFlightCompleted

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Counter") },
                navigationIcon = {
                    IconButton(onClick = onNavigateToSurveys) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back to Surveys")
                    }
                },
                actions = {
                    IconButton(onClick = { showJobDetailsDialog = true }) {
                        Icon(Icons.Default.Info, contentDescription = "Job Details")
                    }
                    IconButton(onClick = { showHistoryDialog = true }) {
                        Icon(Icons.Default.History, contentDescription = "History")
                    }
                    IconButton(
                        onClick = { showCancelConfirmation = true }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "Cancel Survey",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            if (showPostFlightSurvey) {
                scope.launch {
                    // Save current job data and set completion timestamp when survey is shown
                    viewModel.saveCurrentJobData()
                    viewModel.setFlightCompletionTimestamp()
                }
                PilotSurvey(
                    onConfirm = { response ->
                        viewModel.updateSurveyResponse(response)
                        viewModel.selectedJob.value?.let { job ->
                            viewModel.markJobSurveyCompleted(job.id)
                        }
                        viewModel.setShowPostFlightSurvey(false)
                        onNavigateToSurveys()
                    },
                    viewModel = viewModel
                )
            } else if (!isSurveyStarted) {
                // SURVEY START SCREEN
                // Survey not started - show job documents and start survey button
                var imageError by remember { mutableStateOf<String?>(null) }
                var bitmap by remember { mutableStateOf<android.graphics.Bitmap?>(null) }
                var showPdfViewer by remember { mutableStateOf(false) }

                LaunchedEffect(selectedJob?.mapImage) {
                    imageError = null
                    bitmap = null
                    
                    selectedJob?.mapImage?.takeIf { it.isNotEmpty() }?.let { imageData ->
                        try {
                            val decodedBytes = Base64.decode(imageData, Base64.DEFAULT)
                            bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
                        } catch (e: Exception) {
                            imageError = e.message
                        }
                    }
                }

                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    // Show job documents and start survey button when ready
                    if (selectedJob?.mapImage?.isNotEmpty() == true || selectedJob?.mapPdf?.isNotEmpty() == true) {
                        Text(
                            text = "Job Documents",
                            style = MaterialTheme.typography.headlineMedium,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            if (selectedJob?.mapPdf?.isNotEmpty() == true) {
                                Button(
                                    onClick = { showPdfViewer = true },
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Icon(Icons.Default.PictureAsPdf, contentDescription = null)
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text("View PDF")
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                    }

                    if (imageError != null) {
                        Text(
                            text = "Error loading image: $imageError",
                            color = MaterialTheme.colorScheme.error
                        )
                    } else {
                        bitmap?.let {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .fillMaxHeight(0.6f)
                            ) {
                                Image(
                                    bitmap = it.asImageBitmap(),
                                    contentDescription = "Map Image",
                                    modifier = Modifier.fillMaxSize(),
                                    contentScale = ContentScale.Fit
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    Button(
                        onClick = { 
                            // Show pre-flight checklist before starting survey
                            showPreFlightChecklist = true
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp)
                    ) {
                        Text("Start Survey")
                    }
                }

                // PDF viewer dialog
                if (showPdfViewer && selectedJob?.mapPdf?.isNotEmpty() == true) {
                    PdfViewerDialog(
                        pdfBase64 = selectedJob!!.mapPdf,
                        onDismiss = { showPdfViewer = false }
                    )
                }
            } else {
                // Survey started - show pasture management UI
                if (pastures.isEmpty()) {
                    // No pastures yet - show add pasture button
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Button(
                            onClick = { 
                                if (isEquipmentSetupCompleted) {
                                    // Equipment setup already completed, show add pasture dialog directly
                                    showAddPastureDialog = true
                                } else {
                                    // Show equipment setup checklist before adding pasture
                                    showEquipmentSetupChecklist = true
                                }
                            },
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Icon(Icons.Default.Add, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Add First Pasture")
                        }
                    }
                } else {
                    // Pastures exist - show pasture list and management
                    Column(
                        modifier = Modifier.fillMaxSize()
                    ) {
                            // Pasture selection
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = 8.dp, vertical = 4.dp),
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                // Add pasture button
                                IconButton(
                                    onClick = {
                                        if (isEquipmentSetupCompleted) {
                                            // Equipment setup already completed, show add pasture dialog directly
                                            showAddPastureDialog = true
                                        } else {
                                            // Show equipment setup checklist before adding pasture
                                            showEquipmentSetupChecklist = true
                                        }
                                    },
                                    enabled = isSurveyStarted && isEquipmentSetupCompleted
                                ) {
                                    Icon(
                                        Icons.Default.Add,
                                        contentDescription = "Add Pasture",
                                        modifier = Modifier.alpha(if (isSurveyStarted && isEquipmentSetupCompleted) 1f else 0.5f)
                                    )
                                }

                                // Pasture dropdown
                                var expanded by remember { mutableStateOf(false) }
                                Box(
                                    modifier = Modifier.weight(1f)
                                ) {
                                    Button(
                                        onClick = { expanded = true },
                                        modifier = Modifier.fillMaxWidth()
                                    ) {
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceBetween,
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Text(
                                                text = selectedPastureIndex?.let { index ->
                                                    if (index in pastures.indices) {
                                                        pastures[index].name
                                                    } else {
                                                        "Select Pasture"
                                                    }
                                                } ?: "Select Pasture",
                                                maxLines = 1,
                                                modifier = Modifier.weight(1f)
                                            )
                                            Icon(
                                                imageVector = if (expanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                                                contentDescription = if (expanded) "Close dropdown" else "Open dropdown"
                                            )
                                        }
                                    }
                                    DropdownMenu(
                                        expanded = expanded,
                                        onDismissRequest = { expanded = false },
                                        modifier = Modifier.fillMaxWidth(0.9f)
                                    ) {
                                        pastures.forEachIndexed { index, pasture ->
                                            DropdownMenuItem(
                                                text = {
                                                    Row(
                                                        modifier = Modifier.fillMaxWidth(),
                                                        horizontalArrangement = Arrangement.SpaceBetween,
                                                        verticalAlignment = Alignment.CenterVertically
                                                    ) {
                                                        Text(
                                                            text = pasture.name,
                                                            modifier = Modifier.weight(1f)
                                                        )
                                                        IconButton(
                                                            onClick = {
                                                                pastureToDelete = index
                                                                pastureNameToDelete = pasture.name
                                                                showDeletePastureConfirmation = true
                                                                expanded = false
                                                            }
                                                        ) {
                                                            Icon(
                                                                imageVector = Icons.Default.Delete,
                                                                contentDescription = "Delete pasture",
                                                                tint = MaterialTheme.colorScheme.error
                                                            )
                                                        }
                                                    }
                                                },
                                                onClick = {
                                                    viewModel.selectPasture(index)
                                                    expanded = false
                                                }
                                            )
                                        }
                                    }
                                }
                            }



                            // Counter grid for selected pasture
                            selectedPastureIndex?.let { index ->
                                // Safety check to prevent IndexOutOfBoundsException
                                if (index in pastures.indices) {
                                    val pasture = pastures[index]
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .weight(1f) // Take available space minus finish button
                                        .padding(8.dp)
                                ) {
                                    // Add counter button row
                                    Row(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(bottom = 4.dp),
                                        horizontalArrangement = Arrangement.End,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        // Add counter button
                                        Button(
                                            onClick = { showAddCounterDialog = true }
                                        ) {
                                            Icon(Icons.Default.Add, contentDescription = null)
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text("Add Counter")
                                        }
                                    }

                                    // Optimized counter grid with fixed height for consistent appearance
                                    LazyVerticalGrid(
                                        columns = GridCells.Adaptive(minSize = 160.dp),
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .weight(1f), // Take remaining space in column
                                        horizontalArrangement = Arrangement.spacedBy(2.dp),
                                        verticalArrangement = Arrangement.spacedBy(2.dp)
                                    ) {
                                        items(
                                            items = pasture.counters,
                                            key = { it.name }
                                        ) { counter ->
                                            val counterIndex = pasture.counters.indexOf(counter)
                                            Box(
                                                modifier = Modifier.height(120.dp) // Fixed height for all counters
                                            ) {
                                                OptimizedCounterItem(
                                                    counter = counter,
                                                    onValueChange = { newValue ->
                                                        viewModel.updateCounter(counterIndex, newValue)
                                                    },
                                                    onDelete = { viewModel.removeCounter(counterIndex) },
                                                    index = counterIndex,
                                                    onMove = { fromIndex, toIndex ->
                                                        viewModel.reorderCounter(fromIndex, toIndex)
                                                    },
                                                    totalCount = pasture.counters.size
                                                )
                                            }
                                        }
                                    }
                                }
                                }
                            } ?: run {
                                // No pasture selected
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .weight(1f)
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = "Select a pasture",
                                        style = MaterialTheme.typography.bodyLarge
                                    )
                                }
                            }

                        // Finish survey button at the bottom - now part of main Column
                        Button(
                            onClick = {
                                // Show confirmation dialog before finishing survey
                                showFinishSurveyConfirmation = true
                            },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp)
                        ) {
                            Text("Finish Survey")
                        }
                    }
                }
            }
        }
    }

    if (showAddPastureDialog) {
        AddPastureDialog(
            onDismiss = { showAddPastureDialog = false },
            onConfirm = { name, temperature ->
                val success = viewModel.addPasture(name, temperature)
                if (success) {
                    showAddPastureDialog = false
                }
            },
            existingPastureNames = pastures.map { it.name }
        )
    }

    if (showAddCounterDialog) {
        AddCounterDialog(
            onDismiss = { showAddCounterDialog = false },
            onConfirm = { names ->
                names.forEach { name ->
                    viewModel.addCounter(name)
                }
                showAddCounterDialog = false
            },
            existingCounters = selectedPastureIndex?.let { index ->
                if (index in pastures.indices) {
                    pastures[index].counters.map { it.name }
                } else {
                    emptyList()
                }
            } ?: emptyList()
        )
    }

    if (showHistoryDialog) {
        HistoryDialog(
            history = history,
            selectedPasture = selectedPastureIndex?.let { index ->
                if (index in pastures.indices) {
                    pastures[index].name
                } else {
                    null
                }
            },
            onDismiss = { showHistoryDialog = false },
            viewModel = viewModel
        )
    }

    if (showJobDetailsDialog) {
        JobDetailsDialog(
            onDismiss = { showJobDetailsDialog = false },
            viewModel = jobViewModel,
            counterViewModel = viewModel
        )
    }

    if (showFinishSurveyConfirmation) {
        AlertDialog(
            onDismissRequest = { showFinishSurveyConfirmation = false },
            title = { Text("Finish Survey") },
            text = { Text("Are you sure you want to finish the survey?") },
            confirmButton = {
                TextButton(
                                            onClick = {
                            showFinishSurveyConfirmation = false
                            scope.launch {
                                viewModel.saveCurrentJobData()
                            }
                            showPostFlightChecklist = true
                        }
                ) {
                    Text("Yes")
                }
            },
            dismissButton = {
                TextButton(onClick = { showFinishSurveyConfirmation = false }) {
                    Text("No")
                }
            }
        )
    }

    // Cancel confirmation dialog
    if (showCancelConfirmation) {
        AlertDialog(
            onDismissRequest = { showCancelConfirmation = false },
            title = { Text("Cancel Survey") },
            text = { Text("Are you sure you want to cancel this survey? All data for this job will be lost.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showCancelConfirmation = false
                        selectedJob?.let { job ->
                            viewModel.clearJobData(job.id)
                            jobViewModel.clearJobData(job.id)
                        }
                        onNavigateToPilotForm()
                    }
                ) {
                    Text("Cancel Survey", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(onClick = { showCancelConfirmation = false }) {
                    Text("Keep Survey")
                }
            }
        )
    }

    // Delete pasture confirmation dialog
    if (showDeletePastureConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeletePastureConfirmation = false },
            title = { Text("Delete Pasture") },
            text = { Text("Are you sure you want to delete \"$pastureNameToDelete\"? All counters and data for this pasture will be lost.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        pastureToDelete?.let { index ->
                            viewModel.deletePasture(index)
                        }
                        showDeletePastureConfirmation = false
                        pastureToDelete = null
                        pastureNameToDelete = ""
                    }
                ) {
                    Text("Delete", color = MaterialTheme.colorScheme.error)
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDeletePastureConfirmation = false
                        pastureToDelete = null
                        pastureNameToDelete = ""
                    }
                ) {
                    Text("Cancel")
                }
            }
        )
    }

    // Checklist Dialogs
    if (showPreDriveChecklist && !isPreDriveCompleted) {
        PreDriveChecklistDialog(
            onDismiss = { showPreDriveChecklist = false },
            onComplete = {
                showPreDriveChecklist = false
                viewModel.markChecklistCompletedForCurrentJob("pre_drive")
                // The next checklist will be shown automatically by LaunchedEffect
            },
            jobViewModel = jobViewModel,
            counterViewModel = viewModel
        )
    }
    if (showPreFlightChecklist && !isPreFlightCompleted) {
        PreFlightChecklistDialog(
            onDismiss = { showPreFlightChecklist = false },
            onComplete = {
                showPreFlightChecklist = false
                viewModel.markChecklistCompletedForCurrentJob("pre_flight")
                // Start the survey after pre-flight checklist is completed
                viewModel.startSurvey()
            },
            jobViewModel = jobViewModel,
            counterViewModel = viewModel
        )
    }
    if (showEquipmentSetupChecklist && !isEquipmentSetupCompleted) {
        EquipmentSetupChecklistDialog(
            onDismiss = { showEquipmentSetupChecklist = false },
            onComplete = {
                showEquipmentSetupChecklist = false
                viewModel.markChecklistCompletedForCurrentJob("equipment_setup")
                // Show add pasture dialog after equipment setup checklist is completed
                showAddPastureDialog = true
            },
            jobViewModel = jobViewModel,
            counterViewModel = viewModel
        )
    }
    if (showPostFlightChecklist && !isPostFlightCompleted) {
        PostFlightChecklistDialog(
            onDismiss = { showPostFlightChecklist = false },
            onComplete = {
                showPostFlightChecklist = false
                viewModel.markChecklistCompletedForCurrentJob("post_flight")
                // Show survey screen after post-flight checklist
                viewModel.setShowPostFlightSurvey(true)
            },
            jobViewModel = jobViewModel,
            counterViewModel = viewModel
        )
    }
    if (showPostJobChecklist && !isPostJobCompleted) {
        PostJobChecklistDialog(
            onDismiss = { showPostJobChecklist = false },
            onComplete = {
                showPostJobChecklist = false
                viewModel.markChecklistCompletedForCurrentJob("post_job")
                // Handle export logic here
                selectedJob?.let { job ->
                    viewModel.exportJobToGoogleSheets(job) { success ->
                        if (success) {
                            // Navigate back to surveys page after successful export
                            onNavigateToSurveys()
                        } else {
                            // Show export retry dialog on failure
                            exportRetryJobId = job.id
                            exportRetryNewSheetsId = ""
                            showExportRetryDialog = true
                        }
                    }
                }
            },
            jobViewModel = jobViewModel,
            counterViewModel = viewModel
        )
    }

    // Export retry dialog
    if (showExportRetryDialog && exportRetryJobId != null) {
        ExportRetryDialog(
            onDismiss = {
                showExportRetryDialog = false
                exportRetryJobId = null
                exportRetryNewSheetsId = ""
            },
            onRetry = { newSheetsId ->
                exportRetryJobId?.let { jobId ->
                    // Find the job by ID
                    val jobToRetry = viewModel.allJobs.value.find { it.id == jobId }
                    jobToRetry?.let { job ->
                        viewModel.exportJobToGoogleSheetsWithCustomId(job, newSheetsId) { success ->
                            showExportRetryDialog = false
                            exportRetryJobId = null
                            exportRetryNewSheetsId = ""
                            if (success) {
                                // Navigate back to surveys page after successful export
                                onNavigateToSurveys()
                            }
                            // If retry fails, the dialog will close but user can try again from the main screen
                        }
                    }
                }
            },
            newSheetsId = exportRetryNewSheetsId,
            onNewSheetsIdChange = { exportRetryNewSheetsId = it }
        )
    }
}

@Composable
fun PastureItem(
    pasture: Pasture,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp)
            .clickable(onClick = onClick),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Text(
            text = pasture.name,
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun OptimizedCounterItem(
    counter: Counter,
    onValueChange: (Int) -> Unit,
    onDelete: () -> Unit,
    index: Int,
    onMove: (Int, Int) -> Unit,
    totalCount: Int
) {
    // Direct access to counter values for immediate UI updates
    val counterValue = counter.value
    val counterName = counter.name
    
    // Optimized state management
    var showDeleteConfirmation by remember { mutableStateOf(false) }
    var customAmount by remember { mutableStateOf("1") }
    val focusRequester = remember { FocusRequester() }
    
    // Memoize expensive computations
    val canMoveUp by remember { derivedStateOf { index > 0 } }
    val canMoveDown by remember { derivedStateOf { index < totalCount - 1 } }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight()
            .padding(2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .padding(4.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Reorder buttons - optimized with derived state
                    Column {
                        IconButton(
                            onClick = { if (canMoveUp) onMove(index, index - 1) },
                            modifier = Modifier.size(20.dp),
                            enabled = canMoveUp
                        ) {
                            Icon(
                                Icons.Default.KeyboardArrowUp,
                                contentDescription = "Move Up",
                                modifier = Modifier.size(14.dp)
                            )
                        }
                        IconButton(
                            onClick = { if (canMoveDown) onMove(index, index + 1) },
                            modifier = Modifier.size(20.dp),
                            enabled = canMoveDown
                        ) {
                            Icon(
                                Icons.Default.KeyboardArrowDown,
                                contentDescription = "Move Down",
                                modifier = Modifier.size(14.dp)
                            )
                        }
                    }
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = counterName,
                        style = MaterialTheme.typography.titleMedium.copy(
                            lineHeight = MaterialTheme.typography.titleMedium.fontSize * 1.1
                        ),
                        modifier = Modifier.padding(end = 4.dp) // Reduce space to plus button
                    )
                }
                Text(
                    text = "$counterValue",
                    style = MaterialTheme.typography.headlineMedium,
                    modifier = Modifier.padding(end = 16.dp)
                )
            }

            Spacer(modifier = Modifier.height(0.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Optimized increment button with better touch target and visual feedback
                var isIncrementing by remember { mutableStateOf(false) }
                val localScope = rememberCoroutineScope()
                IconButton(
                    onClick = {
                        if (!isIncrementing) {
                            isIncrementing = true
                            customAmount.toIntOrNull()?.let { amount ->
                                onValueChange(counter.value + amount)
                                customAmount = "1"
                            }
                            // Brief delay to prevent rapid clicks
                            localScope.launch {
                                delay(100)
                                isIncrementing = false
                            }
                        }
                    },
                    modifier = Modifier
                        .size(44.dp)
                        .offset(x = 8.dp) // Move 4 pixels to the left (was 12dp, now 8dp)
                        .background(
                            MaterialTheme.colorScheme.primary,
                            RoundedCornerShape(16.dp) // Double the roundness from 8dp to 16dp
                        )
                        .alpha(if (isIncrementing) 0.6f else 1f),
                    enabled = !isIncrementing
                ) {
                    Text(
                        text = "+",
                        style = MaterialTheme.typography.headlineMedium,
                        color = Color.White
                    )
                }
                
                // Optimized TextField with better focus management
                TextField(
                    value = customAmount,
                    onValueChange = { 
                        if (it.isEmpty() || it.toIntOrNull() != null) {
                            customAmount = it
                        }
                    },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier
                        .width(60.dp)
                        .focusRequester(focusRequester),
                    singleLine = true,
                    textStyle = MaterialTheme.typography.bodyLarge
                )
                
                IconButton(
                    onClick = { showDeleteConfirmation = true },
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(Icons.Default.Delete, contentDescription = "Delete")
                }
            }
        }
    }

    if (showDeleteConfirmation) {
        AlertDialog(
            onDismissRequest = { showDeleteConfirmation = false },
            title = { Text("Delete Counter") },
            text = { Text("Are you sure you want to delete $counterName?") },
            confirmButton = {
                TextButton(
                    onClick = {
                        onDelete()
                        showDeleteConfirmation = false
                    }
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteConfirmation = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

// Legacy CounterItem for backward compatibility
@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun CounterItem(
    counter: Counter,
    onValueChange: (Int) -> Unit,
    onDelete: () -> Unit,
    index: Int,
    onMove: (Int, Int) -> Unit,
    totalCount: Int
) {
    OptimizedCounterItem(
        counter = counter,
        onValueChange = onValueChange,
        onDelete = onDelete,
        index = index,
        onMove = onMove,
        totalCount = totalCount
    )
}

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun CustomAmountDialog(
    onDismiss: () -> Unit,
    onIncrement: (Int) -> Unit,
    onDecrement: (Int) -> Unit
) {
    var amount by remember { mutableStateOf("1") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Custom Amount") },
        text = {
            Column {
                TextField(
                    value = amount,
                    onValueChange = { amount = it },
                    label = { Text("Amount") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
            }
        },
        confirmButton = {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TextButton(
                    onClick = { 
                        amount.toIntOrNull()?.let { onDecrement(it) }
                    },
                    enabled = amount.toIntOrNull() != null
                ) {
                    Text("-")
                }
                TextButton(
                    onClick = { 
                        amount.toIntOrNull()?.let { onIncrement(it) }
                    },
                    enabled = amount.toIntOrNull() != null
                ) {
                    Text("+")
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun HistoryDialog(
    history: Map<String, List<CounterChange>>,
    selectedPasture: String?,
    onDismiss: () -> Unit,
    viewModel: CounterViewModel
) {
    Log.d("HistoryDialog", "Selected pasture: $selectedPasture")
    Log.d("HistoryDialog", "History size: ${history.size}")
    
    // Get history for selected pasture
    val pastureHistory = selectedPasture?.let { pastureName ->
        history[pastureName]?.sortedByDescending { it.timestamp }
    } ?: emptyList()
    
    Log.d("HistoryDialog", "Pasture history size: ${pastureHistory.size}")
    
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("History") },
        text = {
            if (pastureHistory.isEmpty()) {
                Text("No history available for this pasture")
            } else {
                LazyColumn {
                    items(pastureHistory) { change ->
                        HistoryItem(
                            change = change,
                            onUndo = { 
                                selectedPasture?.let { pastureName ->
                                    viewModel.undoCounterChange(change, pastureName)
                                }
                            }
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("Close")
            }
        }
    )
}

@Composable
fun HistoryItem(
    change: CounterChange,
    onUndo: () -> Unit
) {
    val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    val formattedDate = dateFormat.format(Date(change.timestamp))
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = change.counterName,
                    style = MaterialTheme.typography.titleMedium
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = formattedDate,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "Change: ${if (change.changeAmount > 0) "+" else ""}${change.changeAmount}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = if (change.changeAmount > 0) 
                        MaterialTheme.colorScheme.primary 
                    else 
                        MaterialTheme.colorScheme.error
                )
            }
            TextButton(onClick = onUndo) {
                Text("Undo")
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddCounterDialog(
    onDismiss: () -> Unit,
    onConfirm: (List<String>) -> Unit,
    existingCounters: List<String>
) {
    var searchQuery by remember { mutableStateOf("") }
    var selectedCounters by remember { mutableStateOf(setOf<String>()) }

    val filteredCounters = remember(searchQuery, existingCounters) {
        presetCounters
            .filter { counter -> 
                !existingCounters.contains(counter) &&
                (searchQuery.isEmpty() || counter.lowercase().contains(searchQuery.lowercase()))
            }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Add Counters") },
        text = {
            Column {
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = { searchQuery = it },
                    label = { Text("Search counters") },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    singleLine = true
                )
                
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 300.dp)
                ) {
                    items(filteredCounters) { counter ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    selectedCounters = if (selectedCounters.contains(counter)) {
                                        selectedCounters - counter
                                    } else {
                                        selectedCounters + counter
                                    }
                                }
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Checkbox(
                                checked = selectedCounters.contains(counter),
                                onCheckedChange = { checked ->
                                    selectedCounters = if (checked) {
                                        selectedCounters + counter
                                    } else {
                                        selectedCounters - counter
                                    }
                                }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = counter,
                                modifier = Modifier.weight(1f)
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                TextButton(
                    onClick = onDismiss
                ) {
                    Text("Cancel")
                }
                TextButton(
                    onClick = { onConfirm(selectedCounters.toList()) },
                    enabled = selectedCounters.isNotEmpty()
                ) {
                    Text("Add Selected")
                }
            }
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddPastureDialog(
    onDismiss: () -> Unit,
    onConfirm: (String, Double) -> Unit,
    existingPastureNames: List<String> = emptyList()
) {
    TwoFieldInputDialog(
        title = "Add New Pasture",
        field1Label = FieldLabels.PASTURE_NAME,
        field2Label = FieldLabels.TEMPERATURE_F,
        field2KeyboardType = KeyboardType.Number,
        field1Validator = { name ->
            ValidationUtils.validatePastureName(name, existingPastureNames)
        },
        field2Validator = { temp ->
            ValidationUtils.validateTemperature(temp)
        },
        onConfirm = { name, temp ->
            try {
                onConfirm(name, temp.toDouble())
            } catch (e: NumberFormatException) {
                // This should be caught by validation, but just in case
            }
        },
        onDismiss = onDismiss
    )
}