package com.example.finalapp.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.foundation.text.KeyboardOptions
import com.example.finalapp.model.SurveyResponse
import com.example.finalapp.viewmodel.CounterViewModel
import android.util.Log

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PilotSurvey(
    onConfirm: (SurveyResponse) -> Unit,
    viewModel: CounterViewModel
) {
    var topography by remember { mutableStateOf("") }
    var cover by remember { mutableStateOf("") }
    var animalDensity by remember { mutableStateOf("") }
    var dayWeather by remember { mutableStateOf("") }
    var missionTypes by remember { mutableStateOf(listOf<String>()) }
    var takeOffsAndLandings by remember { mutableStateOf("0") }
    var additionalNotes by remember { mutableStateOf("") }
    var remarksProceduresManeuvers by remember { mutableStateOf("") }
    var batteryChanges by remember { mutableStateOf("0") }
    var isNight by remember { mutableStateOf(false) }

    var showValidationError by remember { mutableStateOf(false) }
    var validationErrorMessage by remember { mutableStateOf("") }

    val scrollState = rememberScrollState()

    val topographyOptions = listOf("Extreme", "Rough", "Mild", "Light", "Flat")
    val coverOptions = listOf("Very Heavy", "Heavy", "Medium", "Light", "Very Light")
    val animalDensityOptions = listOf("Very Dense", "Dense", "Average", "Thin", "Very Thin")
    val dayWeatherOptions = listOf("Clear", "Mild", "Storming")
    val missionTypeOptions = listOf("Survey", "Visible Camera Add-On", "Game Map Add-On")

    // Save current job data and set completion timestamp when survey screen is opened
    LaunchedEffect(Unit) {
        viewModel.saveCurrentJobData()
        // Set completion timestamp when pilot survey is first opened (flight has ended)
        viewModel.setFlightCompletionTimestamp()
    }

    fun validateForm(): Boolean {
        return topography.isNotBlank() &&
               cover.isNotBlank() &&
               animalDensity.isNotBlank() &&
               dayWeather.isNotBlank() &&
               missionTypes.isNotEmpty() &&
               takeOffsAndLandings.isNotBlank() &&
               takeOffsAndLandings.toIntOrNull() != null &&
               additionalNotes.isNotBlank() &&
               remarksProceduresManeuvers.isNotBlank() &&
               batteryChanges.isNotBlank() &&
               batteryChanges.toIntOrNull() != null
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(scrollState),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Topography
        Text("Topography", style = MaterialTheme.typography.titleMedium)
        Column(
            modifier = Modifier.selectableGroup()
        ) {
            topographyOptions.forEach { option ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = topography == option,
                            onClick = { topography = option }
                        )
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = topography == option,
                        onClick = null
                    )
                    Text(
                        text = option,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
        if (showValidationError && topography.isBlank()) {
            Text("Please select a topography option", color = MaterialTheme.colorScheme.error)
        }

        // Cover
        Text("Cover", style = MaterialTheme.typography.titleMedium)
        Column(
            modifier = Modifier.selectableGroup()
        ) {
            coverOptions.forEach { option ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = cover == option,
                            onClick = { cover = option }
                        )
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = cover == option,
                        onClick = null
                    )
                    Text(
                        text = option,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
        if (showValidationError && cover.isBlank()) {
            Text("Please select a cover option", color = MaterialTheme.colorScheme.error)
        }

        // Animal Density
        Text("Animal Density", style = MaterialTheme.typography.titleMedium)
        Column(
            modifier = Modifier.selectableGroup()
        ) {
            animalDensityOptions.forEach { option ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = animalDensity == option,
                            onClick = { animalDensity = option }
                        )
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = animalDensity == option,
                        onClick = null
                    )
                    Text(
                        text = option,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
        if (showValidationError && animalDensity.isBlank()) {
            Text("Please select an animal density option", color = MaterialTheme.colorScheme.error)
        }

        // Day Weather
        Text("Day Weather", style = MaterialTheme.typography.titleMedium)
        Column(
            modifier = Modifier.selectableGroup()
        ) {
            dayWeatherOptions.forEach { option ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = dayWeather == option,
                            onClick = { dayWeather = option }
                        )
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = dayWeather == option,
                        onClick = null
                    )
                    Text(
                        text = option,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
        if (showValidationError && dayWeather.isBlank()) {
            Text("Please select a day weather option", color = MaterialTheme.colorScheme.error)
        }

        // Day/Night Selection
        Text("Survey Time", style = MaterialTheme.typography.titleMedium)
        Column(
            modifier = Modifier.selectableGroup()
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = !isNight,
                        onClick = { isNight = false }
                    )
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = !isNight,
                    onClick = null
                )
                Text(
                    text = "Day",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .selectable(
                        selected = isNight,
                        onClick = { isNight = true }
                    )
                    .padding(vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = isNight,
                    onClick = null
                )
                Text(
                    text = "Night",
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        // Mission Types
        Text("Mission Type", style = MaterialTheme.typography.titleMedium)
        Column(
            modifier = Modifier.selectableGroup()
        ) {
            missionTypeOptions.forEach { option ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .selectable(
                            selected = missionTypes.contains(option),
                            onClick = {
                                missionTypes = if (missionTypes.contains(option)) {
                                    missionTypes - option
                                } else {
                                    missionTypes + option
                                }
                            }
                        )
                        .padding(vertical = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = missionTypes.contains(option),
                        onCheckedChange = null
                    )
                    Text(
                        text = option,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
        if (showValidationError && missionTypes.isEmpty()) {
            Text("Please select at least one mission type", color = MaterialTheme.colorScheme.error)
        }

        // Take Offs and Landings
        Text("Number of Take Offs and Landing", style = MaterialTheme.typography.titleMedium)
        OutlinedTextField(
            value = takeOffsAndLandings,
            onValueChange = { 
                if (it.isEmpty() || it.toIntOrNull() != null) {
                    takeOffsAndLandings = it
                }
            },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            isError = showValidationError && (takeOffsAndLandings.isEmpty() || takeOffsAndLandings.toIntOrNull() == null)
        )
        if (showValidationError && (takeOffsAndLandings.isEmpty() || takeOffsAndLandings.toIntOrNull() == null)) {
            Text("Please enter a valid number", color = MaterialTheme.colorScheme.error)
        }

        // Additional Notes
        Text("Additional Notes About Survey", style = MaterialTheme.typography.titleMedium)
        OutlinedTextField(
            value = additionalNotes,
            onValueChange = { additionalNotes = it },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            isError = showValidationError && additionalNotes.isBlank()
        )
        if (showValidationError && additionalNotes.isBlank()) {
            Text("This field is required", color = MaterialTheme.colorScheme.error)
        }

        // Remarks, Procedures, Maneuvers
        Text("Remarks, Procedures, Maneuvers (Optional)", style = MaterialTheme.typography.titleMedium)
        OutlinedTextField(
            value = remarksProceduresManeuvers,
            onValueChange = { remarksProceduresManeuvers = it },
            modifier = Modifier.fillMaxWidth(),
            minLines = 3,
            placeholder = { Text("Enter any remarks, procedures, or maneuvers") },
            isError = showValidationError && remarksProceduresManeuvers.isBlank()
        )
        if (showValidationError && remarksProceduresManeuvers.isBlank()) {
            Text("This field is required", color = MaterialTheme.colorScheme.error)
        }

        // Number of Battery Changes
        Text("Number of Battery Changes", style = MaterialTheme.typography.titleMedium)
        OutlinedTextField(
            value = batteryChanges,
            onValueChange = { 
                if (it.isEmpty() || it.toIntOrNull() != null) {
                    batteryChanges = it
                }
            },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
            modifier = Modifier.fillMaxWidth(),
            isError = showValidationError && (batteryChanges.isEmpty() || batteryChanges.toIntOrNull() == null)
        )
        if (showValidationError && (batteryChanges.isEmpty() || batteryChanges.toIntOrNull() == null)) {
            Text("Please enter a valid number", color = MaterialTheme.colorScheme.error)
        }



        Spacer(modifier = Modifier.height(24.dp))

        // Submit button
                    Button(
                onClick = {
                    if (validateForm()) {
                        val existingSurveyResponse = viewModel.surveyResponse.value
                        Log.d("PilotSurvey", "Existing survey response: $existingSurveyResponse")

                        // Use existing timestamps from the survey response
                        val startTimestamp = existingSurveyResponse?.startTimestamp ?: run {
                            Log.w("PilotSurvey", "No valid start timestamp found, using current time")
                            System.currentTimeMillis()
                        }
                        val completionTimestamp = existingSurveyResponse?.completionTimestamp ?: run {
                            Log.w("PilotSurvey", "No completion timestamp found, using current time")
                            System.currentTimeMillis()
                        }

                        // Add logging for survey completion
                        Log.d("PilotSurvey", "Survey form submission:")
                        Log.d("PilotSurvey", "startTimestamp: $startTimestamp")
                        Log.d("PilotSurvey", "completionTimestamp: $completionTimestamp")
                        Log.d("PilotSurvey", "Flight duration: ${completionTimestamp - startTimestamp} ms")

                        val surveyResponse = SurveyResponse(
                            topography = topography,
                            cover = cover,
                            animalDensity = animalDensity,
                            dayWeather = dayWeather,
                            missionTypes = missionTypes,
                            takeOffsAndLandings = takeOffsAndLandings.toIntOrNull() ?: 0,
                            additionalNotes = additionalNotes,
                            remarksProceduresManeuvers = remarksProceduresManeuvers,
                            batteryChanges = batteryChanges.toIntOrNull() ?: 0,
                            startTimestamp = startTimestamp,
                            completionTimestamp = completionTimestamp,
                            isNight = isNight
                        )
                        onConfirm(surveyResponse)
                    } else {
                        showValidationError = true
                        validationErrorMessage = "Please fill in all required fields correctly"
                    }
                },
                modifier = Modifier.fillMaxWidth()
        ) {
            Text("Finish Survey")
        }
    }

    if (showValidationError) {
        AlertDialog(
            onDismissRequest = { showValidationError = false },
            title = { Text("Validation Error") },
            text = { Text(validationErrorMessage) },
            confirmButton = {
                TextButton(onClick = { showValidationError = false }) {
                    Text("OK")
                }
            }
        )
    }
} 