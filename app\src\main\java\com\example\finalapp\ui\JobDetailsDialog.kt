package com.example.finalapp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.finalapp.model.Job
import com.example.finalapp.model.Survey
import com.example.finalapp.viewmodel.JobViewModel
import com.example.finalapp.viewmodel.CounterViewModel
import android.graphics.BitmapFactory
import android.util.Base64
import androidx.compose.ui.window.DialogProperties
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PictureAsPdf
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Close

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun JobDetailsDialog(
    onDismiss: () -> Unit,
    viewModel: JobViewModel,
    counterViewModel: CounterViewModel = viewModel(),
    surveyForPreview: Survey? = null // Survey data to show when job hasn't been created yet
) {
    val selectedJob by counterViewModel.selectedJob.collectAsState()
    val pilotInfoMap by viewModel.pilotInfo.collectAsState()
    var imageError by remember { mutableStateOf<String?>(null) }
    var bitmap by remember { mutableStateOf<android.graphics.Bitmap?>(null) }
    var showFullScreenImage by remember { mutableStateOf(false) }
    var showPdfViewer by remember { mutableStateOf(false) }

    // Load image when job or survey changes
    LaunchedEffect(selectedJob?.mapImage, surveyForPreview?.image) {
        imageError = null
        bitmap = null

        // Try to load image from job first, then from survey
        val imageData = selectedJob?.mapImage?.takeIf { it.isNotEmpty() }
            ?: surveyForPreview?.image?.data?.let { imageDataList ->
                // Convert List<Int> to ByteArray and then to Base64
                val byteArray = ByteArray(imageDataList.size) { index -> imageDataList[index].toByte() }
                Base64.encodeToString(byteArray, Base64.DEFAULT)
            }

        imageData?.let { data ->
            try {
                val decodedBytes = Base64.decode(data, Base64.DEFAULT)
                bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
            } catch (e: Exception) {
                imageError = e.message
            }
        }
    }

    // Show dialog if we have either a job or survey data
    if (selectedJob != null || surveyForPreview != null) {
        val job = selectedJob
        val survey = surveyForPreview
        val pilotInfo = job?.let { pilotInfoMap[it.id] }

        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text(if (job != null) "Job Details" else "Survey Details") },
            text = {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Pilot Information (only for jobs)
                    pilotInfo?.let { info ->
                        DetailItem("Pilot Name", info.name)
                        DetailItem("Drone Name", info.droneName)
                    }

                    // Details from job or survey
                    DetailItem("Property Name", job?.name ?: survey?.property_name ?: "")
                    DetailItem("Coordinates", job?.coordinates ?: survey?.coordinates ?: "")
                    DetailItem("Gate Code", job?.gateCode ?: survey?.gate_code ?: "")
                    DetailItem("Additional Notes", job?.additionalNotes ?: survey?.notes ?: "")
                    DetailItem("Vehicle Requirements", job?.vehicleRequirements ?: survey?.driving_instructions ?: "")

                    // Display image and PDF options
                    val hasImage = job?.mapImage?.isNotEmpty() == true || survey?.image != null
                    val hasPdf = job?.mapPdf?.isNotEmpty() == true || survey?.pdf != null

                    if (hasImage || hasPdf) {
                        Text(
                            text = "Documents",
                            style = MaterialTheme.typography.titleSmall,
                            color = MaterialTheme.colorScheme.primary
                        )
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            // PDF button only (image is already shown)
                            if (hasPdf) {
                                Button(
                                    onClick = { showPdfViewer = true },
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    Icon(Icons.Default.PictureAsPdf, contentDescription = null)
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text("View LOA")
                                }
                            }
                        }
                        
                        // Show image preview if available
                        if (imageError != null) {
                            Text(
                                text = "Error loading image: $imageError",
                                color = MaterialTheme.colorScheme.error
                            )
                        } else {
                            bitmap?.let {
                                Image(
                                    bitmap = it.asImageBitmap(),
                                    contentDescription = "Map Image",
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .height(200.dp)
                                        .clickable { showFullScreenImage = true },
                                    contentScale = ContentScale.Fit
                                )
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = onDismiss) {
                    Text("Close")
                }
            }
        )
    }

    // Full screen image preview
    if (showFullScreenImage && bitmap != null) {
        AlertDialog(
            onDismissRequest = { showFullScreenImage = false },
            properties = DialogProperties(usePlatformDefaultWidth = false),
            content = {
                Surface(
                    modifier = Modifier
                        .fillMaxSize()
                        .clickable { showFullScreenImage = false },
                    color = MaterialTheme.colorScheme.background
                ) {
                    Image(
                        bitmap = bitmap!!.asImageBitmap(),
                        contentDescription = "Full Screen Map Image",
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Fit
                    )
                }
            }
        )
    }

    // PDF viewer dialog
    if (showPdfViewer) {
        val pdfData = selectedJob?.mapPdf?.takeIf { it.isNotEmpty() }
            ?: surveyForPreview?.pdf?.data?.let { pdfDataList ->
                // Convert List<Int> to ByteArray and then to Base64
                val byteArray = ByteArray(pdfDataList.size) { index -> pdfDataList[index].toByte() }
                Base64.encodeToString(byteArray, Base64.DEFAULT)
            }

        pdfData?.let { data ->
            PdfViewerDialog(
                pdfBase64 = data,
                onDismiss = { showPdfViewer = false }
            )
        }
    }
}

@Composable
private fun DetailItem(label: String, value: String) {
    Column {
        Text(
            text = label,
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium
        )
    }
} 