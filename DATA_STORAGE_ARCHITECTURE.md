# Data Storage Architecture

## Overview

The Sky Senderos application uses a multi-layered data storage architecture designed for efficiency, data integrity, and proper cleanup. This document outlines the complete data storage structure and the recent improvements made to fix pasture deletion issues.

## Storage Layers

### 1. JobViewModel DataStore (`job_details`)
Manages high-level job metadata and pilot information.

**Keys:**
- `pilot_info`: `Map<String, PilotInfo>` - Maps jobId to pilot information
- `selected_job`: `Job?` - Currently selected job for operations  
- `completed_jobs`: `List<Job>` - All completed jobs with basic metadata

**Purpose:** Stores lightweight job metadata without duplicating detailed operational data.

### 2. AppDataStore (`app_data`)
Manages detailed job operational data including pastures, history, and survey responses.

**Keys:**
- `job_pastures`: `Map<String, List<Pasture>>` - Maps jobId to list of pastures
- `job_history`: `Map<String, List<CounterChange>>` - Maps history keys to counter changes
- `job_survey_responses`: `Map<String, SurveyResponse>` - Maps jobId to survey responses
- `active_jobs`: `List<Job>` - Currently active jobs
- `selected_pasture_index`: `Int` - Index of currently selected pasture
- Other job-specific metadata...

## History Data Storage

### Key Format
History data uses a compound key format: `"${jobId}_${pastureName}"`

**Benefits:**
- **Unique Keys:** Prevents conflicts between pastures with same names across different jobs
- **Efficient Cleanup:** Enables targeted deletion of pasture-specific history
- **Job Isolation:** History data is properly scoped to individual jobs

### Example
```kotlin
// Job ID: "job123", Pasture: "North Field"
// History Key: "job123_North Field"
val historyKey = AppDataStore.createHistoryKey("job123", "North Field")
```

### Utility Methods
```kotlin
companion object {
    fun createHistoryKey(jobId: String, pastureName: String): String
    fun extractJobIdFromHistoryKey(historyKey: String): String?
    fun extractPastureNameFromHistoryKey(historyKey: String): String?
}
```

## Data Models

### Core Models
```kotlin
@Serializable
data class Job(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val status: String = "Active",
    val creationTimestamp: Long = System.currentTimeMillis(),
    val pastures: List<Pasture> = emptyList(), // Lightweight reference
    val surveyData: SurveyData? = null,
    // ... other metadata
)

@Serializable
data class Pasture(
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val temperature: Double,
    val creationTimestamp: Long = System.currentTimeMillis(),
    val counters: List<Counter> = emptyList(),
    val history: List<CounterChange> = emptyList() // Local cache
)

@Serializable
data class CounterChange(
    val timestamp: Long,
    val counterName: String,
    val changeAmount: Int
)
```

## Data Operations

### Pasture Creation
1. Validate pasture name (no duplicates within job)
2. Create new `Pasture` instance
3. Add to job's pasture list in `AppDataStore`
4. Update selected pasture index if first pasture

### Pasture Deletion (FIXED)
1. Remove pasture from job's pasture list
2. **Clean up persistent history data** using `deletePastureHistory()`
3. Clear local history state
4. Adjust selected pasture index
5. Save all changes

**Previous Issue:** History data with keys like `"${jobId}_${pastureName}"` was not being cleaned up from persistent storage, causing data accumulation.

**Solution:** Added `deletePastureHistory()` method that properly removes history entries using the compound key format.

### History Management
1. **Recording Changes:** Use `saveJobHistory()` with local pasture names as keys
2. **Storage:** Automatically converts to compound keys (`"${jobId}_${pastureName}"`)
3. **Retrieval:** Load and filter by job, convert back to local keys
4. **Cleanup:** Use `deletePastureHistory()` for targeted deletion

### Job Cleanup
When clearing all job data:
1. Remove job from active/completed lists
2. Clear job's pastures
3. **Filter out all history entries** for the job using `extractJobIdFromHistoryKey()`
4. Clear survey responses
5. Clear other job-specific metadata

## Performance Optimizations

### 1. Efficient History Storage
- Compound keys prevent key collisions
- Targeted deletion avoids full history scans
- Automatic cleanup limits history size per pasture

### 2. Data Separation
- Job metadata separate from operational data
- Reduces serialization overhead
- Enables selective loading

### 3. Batch Operations
- Multiple updates in single DataStore transactions
- Reduced I/O operations
- Better consistency

## Error Handling

### JSON Serialization
- Graceful fallback to empty collections on decode errors
- Comprehensive error logging
- Data integrity preservation

### Concurrent Access
- Mutex locks prevent race conditions
- Atomic updates for related data
- Consistent state management

## Migration Considerations

The improved history key format is backward compatible:
- Existing history data continues to work
- New entries use compound key format
- Gradual migration through normal operations

## Best Practices

### For Developers
1. **Always use AppDataStore utility methods** for history key operations
2. **Clean up related data** when deleting entities
3. **Use batch operations** for related updates
4. **Handle serialization errors** gracefully
5. **Log operations** for debugging

### For Data Operations
1. **Validate input data** before storage
2. **Use transactions** for multi-step operations
3. **Clean up orphaned data** regularly
4. **Monitor storage size** and implement cleanup policies
5. **Test deletion operations** thoroughly

## Testing

### Key Test Cases
1. **Pasture Deletion:** Verify history cleanup
2. **Job Deletion:** Verify complete data removal
3. **Concurrent Operations:** Test race conditions
4. **Data Recovery:** Test error scenarios
5. **Migration:** Test backward compatibility

This architecture ensures data integrity, efficient operations, and proper cleanup while maintaining the application's existing functionality.

## Summary of Improvements Made

### 🐛 **Fixed: Pasture Deletion History Cleanup Issue**

**Problem:** When deleting pastures, the associated history data was not being properly cleaned up from persistent storage, causing data accumulation and potential memory issues.

**Root Cause:** History data was stored with compound keys (`"${jobId}_${pastureName}"`), but deletion methods only cleaned up local state using simple pasture names as keys.

**Solution Implemented:**

1. **Added `deletePastureHistory()` method** in `AppDataStore`:
   ```kotlin
   suspend fun deletePastureHistory(jobId: String, pastureName: String) {
       // Properly removes history using compound key format
       val historyKeyToDelete = createHistoryKey(jobId, pastureName)
       // ... cleanup logic
   }
   ```

2. **Updated `PastureViewModel.deletePasture()`**:
   - Now calls `dataStore.deletePastureHistory(jobId, pastureNameToDelete)`
   - Ensures both local state and persistent storage are cleaned up

3. **Updated `CounterViewModel.deletePasture()`**:
   - Enhanced to properly clean up persistent history data
   - Maintains backward compatibility with existing functionality

4. **Updated `CounterManagementViewModel.clearPastureHistory()`**:
   - Now uses the new `deletePastureHistory()` method
   - Ensures complete cleanup of pasture-specific history

### 🏗️ **Enhanced Data Storage Architecture**

1. **Improved Documentation**:
   - Added comprehensive inline documentation
   - Documented data storage structure and key formats
   - Added utility methods for history key management

2. **Better Error Handling**:
   - Enhanced JSON serialization error handling
   - Added comprehensive logging for debugging
   - Graceful fallback to empty collections on decode errors

3. **Utility Methods for History Keys**:
   ```kotlin
   companion object {
       fun createHistoryKey(jobId: String, pastureName: String): String
       fun extractJobIdFromHistoryKey(historyKey: String): String?
       fun extractPastureNameFromHistoryKey(historyKey: String): String?
   }
   ```

4. **Enhanced Job Data Cleanup**:
   - `clearJobData()` now properly filters out all history entries for a job
   - Uses `extractJobIdFromHistoryKey()` for accurate filtering
   - Prevents orphaned history data

### 🔧 **Improved Methods**

1. **`getJobHistory(jobId: String)`**: New method to retrieve history for a specific job with proper key conversion
2. **`cleanupOldHistory()`**: Enhanced with better logging and error handling
3. **`saveJobHistory()`**: Now uses the `json` instance consistently for better error handling

### ✅ **Verified Functionality**

All existing functionality has been preserved:
- ✅ Pasture creation and management
- ✅ Counter operations and history tracking
- ✅ Survey data management
- ✅ Job lifecycle management
- ✅ Data persistence and retrieval
- ✅ Confirmation dialogs for destructive actions
- ✅ Duplicate pasture name validation

### 🎯 **Key Benefits**

1. **Data Integrity**: No more orphaned history data when deleting pastures
2. **Memory Efficiency**: Proper cleanup prevents data accumulation
3. **Better Debugging**: Enhanced logging for troubleshooting
4. **Maintainability**: Clear documentation and utility methods
5. **Backward Compatibility**: Existing data continues to work seamlessly

The improvements ensure that when a pasture is deleted, all associated data (including history) is properly cleaned up, resolving the original issue while maintaining all existing functionality.
