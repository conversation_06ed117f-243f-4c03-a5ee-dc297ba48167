package com.example.finalapp.service

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.HttpURLConnection
import java.net.URL


class ApiService {
    companion object {
        private const val TAG = "ApiService"
        private const val API_URL = "https://zhtq9c7gge.execute-api.us-east-2.amazonaws.com/prod/surveys"
        private const val API_KEY = "Sky193124141233"
    }

    suspend fun deleteSurvey(pilot: String, droneName: String, datetime: String): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Deleting survey for pilot: $pilot, drone: $droneName, datetime: $datetime")
            
            val urlString = "$API_URL?pilot=$pilot&drone_name=$droneName&datetime=$datetime"
            Log.d(TAG, "DELETE URL: $urlString")
            
            val url = URL(urlString)
            val connection = url.openConnection() as HttpURLConnection
            connection.requestMethod = "DELETE"
            connection.setRequestProperty("x-api-key", API_KEY)
            connection.connectTimeout = 10000 // 10 seconds
            connection.readTimeout = 10000 // 10 seconds
            
            try {
                Log.d(TAG, "Connecting to server for DELETE request...")
                connection.connect()
                val responseCode = connection.responseCode
                Log.d(TAG, "DELETE response code: $responseCode")
                
                if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_NO_CONTENT) {
                    Log.d(TAG, "Survey deleted successfully")
                    true
                } else {
                    val errorStream = connection.errorStream?.bufferedReader()?.use { it.readText() }
                    Log.e(TAG, "DELETE failed with response code: $responseCode, error: $errorStream")
                    false
                }
            } finally {
                connection.disconnect()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting survey", e)
            false
        }
    }
} 