package com.example.finalapp.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.finalapp.data.AppDataStore
import com.example.finalapp.model.Counter
import com.example.finalapp.model.CounterChange
import com.example.finalapp.model.Pasture
import com.example.finalapp.util.ValidationUtils
import com.example.finalapp.util.Constants
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * ViewModel responsible for counter management within pastures
 */
class CounterManagementViewModel(
    application: Application,
    private val pastureViewModel: PastureViewModel
) : AndroidViewModel(application) {
    
    private val dataStore = AppDataStore(application)
    private val counterUpdateMutex = Mutex()
    
    // History tracking
    private val _history = MutableStateFlow<Map<String, List<CounterChange>>>(emptyMap())
    val history: StateFlow<Map<String, List<CounterChange>>> = _history.asStateFlow()
    
    init {
        // Load history from DataStore
        viewModelScope.launch {
            combine(
                dataStore.jobHistory,
                pastureViewModel.selectedJobId
            ) { jobHistory, selectedJobId ->
                selectedJobId?.let { jobId ->
                    // Filter history for current job and remove job ID prefix
                    val filteredHistory = jobHistory.filterKeys { it.startsWith("${jobId}_") }
                        .mapKeys { it.key.substringAfter("${jobId}_") }
                    Log.d("CounterManagementViewModel", "Loaded history for ${filteredHistory.size} pastures")
                    _history.value = filteredHistory
                }
            }.collect()
        }
    }
    
    /**
     * Add a counter to the selected pasture
     */
    suspend fun addCounter(name: String): Result<Unit> {
        val selectedPasture = pastureViewModel.selectedPasture.value 
            ?: return Result.failure(Exception("No pasture selected"))
        val selectedIndex = pastureViewModel.selectedPastureIndex.value 
            ?: return Result.failure(Exception("No pasture selected"))
        
        // Validate counter name
        val validationResult = ValidationUtils.validateCounterName(
            name,
            selectedPasture.counters.map { it.name }
        )
        if (!validationResult.isValid) {
            return Result.failure(Exception(validationResult.errorMessage))
        }
        
        return try {
            counterUpdateMutex.withLock {
                val newCounter = Counter(name = name.trim())
                val updatedPasture = selectedPasture.copy(
                    counters = selectedPasture.counters + newCounter
                )
                
                pastureViewModel.updatePastureCounters(selectedIndex, updatedPasture)
                Log.d("CounterManagementViewModel", "Added counter: $name")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("CounterManagementViewModel", "Error adding counter", e)
            Result.failure(e)
        }
    }
    
    /**
     * Remove a counter from the selected pasture
     */
    suspend fun removeCounter(counterIndex: Int): Result<Unit> {
        val selectedPasture = pastureViewModel.selectedPasture.value
            ?: return Result.failure(Exception("No pasture selected"))
        val selectedPastureIndex = pastureViewModel.selectedPastureIndex.value
            ?: return Result.failure(Exception("No pasture selected"))
        val jobId = pastureViewModel.selectedJobId.value
            ?: return Result.failure(Exception("No job selected"))

        if (counterIndex !in selectedPasture.counters.indices) {
            return Result.failure(Exception("Invalid counter index"))
        }

        return try {
            counterUpdateMutex.withLock {
                val counterToDelete = selectedPasture.counters[counterIndex]
                val updatedCounters = selectedPasture.counters.toMutableList().apply {
                    removeAt(counterIndex)
                }
                val updatedPasture = selectedPasture.copy(counters = updatedCounters)

                // Clean up history data for the deleted counter
                _history.update { currentHistory ->
                    val pastureHistory = currentHistory[selectedPasture.name] ?: emptyList()
                    val filteredHistory = pastureHistory.filter { change ->
                        change.counterName != counterToDelete.name
                    }
                    if (filteredHistory.isEmpty()) {
                        currentHistory - selectedPasture.name
                    } else {
                        currentHistory + (selectedPasture.name to filteredHistory)
                    }
                }

                // Save updated history to DataStore
                dataStore.saveJobHistory(jobId, _history.value)

                pastureViewModel.updatePastureCounters(selectedPastureIndex, updatedPasture)
                Log.d("CounterManagementViewModel", "Removed counter '${counterToDelete.name}' at index: $counterIndex and cleaned up its history")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("CounterManagementViewModel", "Error removing counter", e)
            Result.failure(e)
        }
    }
    
    /**
     * Update counter value and record history
     */
    suspend fun updateCounter(counterIndex: Int, newValue: Int): Result<Unit> {
        val selectedPasture = pastureViewModel.selectedPasture.value 
            ?: return Result.failure(Exception("No pasture selected"))
        val selectedPastureIndex = pastureViewModel.selectedPastureIndex.value 
            ?: return Result.failure(Exception("No pasture selected"))
        val jobId = pastureViewModel.selectedJobId.value 
            ?: return Result.failure(Exception("No job selected"))
        
        if (counterIndex !in selectedPasture.counters.indices) {
            return Result.failure(Exception("Invalid counter index"))
        }
        
        // Validate counter value
        val validationResult = ValidationUtils.validateCounterValue(newValue.toString())
        if (!validationResult.isValid) {
            return Result.failure(Exception(validationResult.errorMessage))
        }
        
        return try {
            counterUpdateMutex.withLock {
                val counter = selectedPasture.counters[counterIndex]
                val oldValue = counter.value
                val changeAmount = newValue - oldValue
                
                // Update counter value
                val updatedCounters = selectedPasture.counters.toMutableList().apply {
                    this[counterIndex] = counter.copy(value = newValue)
                }
                val updatedPasture = selectedPasture.copy(counters = updatedCounters)
                
                // Record history if there was a significant change
                if (kotlin.math.abs(changeAmount) >= Constants.DataStore.MIN_CHANGE_THRESHOLD) {
                    val change = CounterChange(
                        timestamp = System.currentTimeMillis(),
                        counterName = counter.name,
                        changeAmount = changeAmount
                    )
                    
                    _history.update { currentHistory ->
                        val pastureHistory = currentHistory[selectedPasture.name] ?: emptyList()
                        val updatedPastureHistory = (pastureHistory + change)
                            .takeLast(Constants.DataStore.MAX_HISTORY_ITEMS_PER_JOB)
                        currentHistory + (selectedPasture.name to updatedPastureHistory)
                    }
                    
                    // Save history to DataStore
                    dataStore.saveJobHistory(jobId, _history.value)
                }
                
                pastureViewModel.updatePastureCounters(selectedPastureIndex, updatedPasture)
                Log.d("CounterManagementViewModel", "Updated counter ${counter.name}: $oldValue -> $newValue")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("CounterManagementViewModel", "Error updating counter", e)
            Result.failure(e)
        }
    }
    
    /**
     * Increment counter by a specific amount
     */
    suspend fun incrementCounter(counterIndex: Int, amount: Int = 1): Result<Unit> {
        val selectedPasture = pastureViewModel.selectedPasture.value 
            ?: return Result.failure(Exception("No pasture selected"))
        
        if (counterIndex !in selectedPasture.counters.indices) {
            return Result.failure(Exception("Invalid counter index"))
        }
        
        val currentValue = selectedPasture.counters[counterIndex].value
        val newValue = (currentValue + amount).coerceAtLeast(Constants.Counter.MIN_VALUE)
            .coerceAtMost(Constants.Counter.MAX_VALUE)
        
        return updateCounter(counterIndex, newValue)
    }
    
    /**
     * Decrement counter by a specific amount
     */
    suspend fun decrementCounter(counterIndex: Int, amount: Int = 1): Result<Unit> {
        return incrementCounter(counterIndex, -amount)
    }
    
    /**
     * Reorder counters within a pasture
     */
    suspend fun reorderCounter(fromIndex: Int, toIndex: Int): Result<Unit> {
        val selectedPasture = pastureViewModel.selectedPasture.value 
            ?: return Result.failure(Exception("No pasture selected"))
        val selectedPastureIndex = pastureViewModel.selectedPastureIndex.value 
            ?: return Result.failure(Exception("No pasture selected"))
        
        if (fromIndex !in selectedPasture.counters.indices || 
            toIndex !in selectedPasture.counters.indices) {
            return Result.failure(Exception("Invalid counter indices"))
        }
        
        return try {
            counterUpdateMutex.withLock {
                val updatedCounters = selectedPasture.counters.toMutableList().apply {
                    val item = removeAt(fromIndex)
                    add(toIndex, item)
                }
                val updatedPasture = selectedPasture.copy(counters = updatedCounters)
                
                pastureViewModel.updatePastureCounters(selectedPastureIndex, updatedPasture)
                Log.d("CounterManagementViewModel", "Reordered counter from $fromIndex to $toIndex")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("CounterManagementViewModel", "Error reordering counter", e)
            Result.failure(e)
        }
    }
    
    /**
     * Undo a counter change
     */
    suspend fun undoCounterChange(change: CounterChange, pastureName: String): Result<Unit> {
        val selectedPasture = pastureViewModel.selectedPasture.value
            ?: return Result.failure(Exception("No pasture selected"))
        val selectedPastureIndex = pastureViewModel.selectedPastureIndex.value
            ?: return Result.failure(Exception("No pasture selected"))
        val jobId = pastureViewModel.selectedJobId.value
            ?: return Result.failure(Exception("No job selected"))

        if (selectedPasture.name != pastureName) {
            return Result.failure(Exception("Selected pasture does not match change pasture"))
        }

        return try {
            counterUpdateMutex.withLock {
                val counterIndex = selectedPasture.counters.indexOfFirst { it.name == change.counterName }
                if (counterIndex == -1) {
                    return Result.failure(Exception("Counter not found"))
                }

                val counter = selectedPasture.counters[counterIndex]
                val newValue = (counter.value - change.changeAmount)
                    .coerceAtLeast(Constants.Counter.MIN_VALUE)
                    .coerceAtMost(Constants.Counter.MAX_VALUE)

                val updatedCounters = selectedPasture.counters.toMutableList().apply {
                    this[counterIndex] = counter.copy(value = newValue)
                }
                val updatedPasture = selectedPasture.copy(counters = updatedCounters)

                // Remove the change from history
                _history.update { currentHistory ->
                    val pastureHistory = currentHistory[pastureName] ?: emptyList()
                    val updatedPastureHistory = pastureHistory.filter { it != change }
                    currentHistory + (pastureName to updatedPastureHistory)
                }

                pastureViewModel.updatePastureCounters(selectedPastureIndex, updatedPasture)
                dataStore.saveJobHistory(jobId, _history.value)

                Log.d("CounterManagementViewModel", "Undid change for ${change.counterName}")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e("CounterManagementViewModel", "Error undoing counter change", e)
            Result.failure(e)
        }
    }
    
    /**
     * Get history for a specific pasture
     */
    fun getHistoryForPasture(pastureName: String): List<CounterChange> {
        return _history.value[pastureName] ?: emptyList()
    }
    
    /**
     * Clear history for a specific pasture
     */
    suspend fun clearPastureHistory(pastureName: String) {
        val jobId = pastureViewModel.selectedJobId.value ?: return

        try {
            // Clear from local state
            _history.update { currentHistory ->
                currentHistory - pastureName
            }

            // Clear from persistent storage
            dataStore.deletePastureHistory(jobId, pastureName)

            // Save updated local history state
            dataStore.saveJobHistory(jobId, _history.value)

            Log.d("CounterManagementViewModel", "Cleared history for pasture: $pastureName")
        } catch (e: Exception) {
            Log.e("CounterManagementViewModel", "Error clearing pasture history", e)
        }
    }
    
    /**
     * Get total count for all counters in current pasture
     */
    fun getTotalCountForCurrentPasture(): Int {
        return pastureViewModel.selectedPasture.value?.counters?.sumOf { it.value } ?: 0
    }
    
    /**
     * Get counts for all pastures in current job
     */
    fun getPastureCounts(): Map<String, Int> {
        return pastureViewModel.pastures.value.associate { pasture ->
            pasture.name to pasture.counters.sumOf { it.value }
        }
    }
}
