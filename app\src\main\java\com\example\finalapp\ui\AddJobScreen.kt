package com.example.finalapp.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.finalapp.viewmodel.JobViewModel
import com.example.finalapp.viewmodel.CounterViewModel
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.Date
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.decodeFromString
import java.net.URL
import java.net.HttpURLConnection
import androidx.compose.foundation.clickable
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.TimeZone
import android.util.Log
import java.io.IOException
import com.example.finalapp.model.Survey
import com.example.finalapp.model.SurveyResponse
import com.example.finalapp.model.Job
import com.example.finalapp.model.PilotInfo
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import com.example.finalapp.ui.PreDriveChecklistDialog

// This screen is the 'Add Job' page, where a new job is created by the user.
// It was previously called PilotFormScreen for historical reasons.

@Serializable
data class ApiSurveyResponse(
    val data: List<Survey>
)

private val json = Json { 
    ignoreUnknownKeys = true
    coerceInputValues = true
    isLenient = true
}

private val inputDateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US).apply {
    timeZone = TimeZone.getTimeZone("UTC")
}

private val displayDateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.US)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddJobScreen(
    onFormComplete: () -> Unit,
    onNavigateToSurveys: () -> Unit,
    viewModel: JobViewModel = viewModel(),
    counterViewModel: CounterViewModel = viewModel()
) {
    var pilotName by remember { mutableStateOf("") }
    var droneName by remember { mutableStateOf("") }
    var showError by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var isLoading by remember { mutableStateOf(false) }
    var isSubmitting by remember { mutableStateOf(false) }
    var showSurveySelection by remember { mutableStateOf(false) }
    var surveys by remember { mutableStateOf<List<Survey>>(emptyList()) }
    var selectedSurvey by remember { mutableStateOf<Survey?>(null) }
    var showPreDriveChecklist by remember { mutableStateOf(false) }
    var selectedSurveyForChecklist by remember { mutableStateOf<Survey?>(null) }
    var expanded by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()
    val droneOptions = listOf("SPUR", "VAQUERO", "SMOKEY", "TEX", "COLT", "BEVO", "PECOS", "RANGER")

    fun fetchSurveys() {
        scope.launch {
            try {
                isLoading = true
                errorMessage = null
                
                withContext(Dispatchers.IO) {
                    val API_URL = "https://zhtq9c7gge.execute-api.us-east-2.amazonaws.com/prod/surveys"
                    val API_KEY = "Sky193124141233"
                    
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
                    dateFormat.timeZone = TimeZone.getTimeZone("UTC")
                    val currentDateTime = dateFormat.format(Date())
                    
                    val urlString = "$API_URL?pilot=$pilotName&drone_name=$droneName&datetime=$currentDateTime"
                    Log.d("AddJobScreen", "Fetching surveys from URL: $urlString")
                    
                    try {
                        val url = URL(urlString)
                        Log.d("AddJobScreen", "Attempting to connect to: ${url.host}")
                        
                        val connection = url.openConnection() as HttpURLConnection
                        connection.requestMethod = "GET"
                        connection.setRequestProperty("x-api-key", API_KEY)
                        connection.connectTimeout = 10000 // 10 seconds
                        connection.readTimeout = 10000 // 10 seconds
                        
                        try {
                            Log.d("AddJobScreen", "Connecting to server...")
                            connection.connect()
                            val responseCode = connection.responseCode
                            Log.d("AddJobScreen", "Response code: $responseCode")
                            
                            if (responseCode == HttpURLConnection.HTTP_OK) {
                                val response = connection.inputStream.bufferedReader().use { it.readText() }
                                Log.d("AddJobScreen", "Response body: $response")
                                
                                val surveyResponse = json.decodeFromString<ApiSurveyResponse>(response)
                                surveys = surveyResponse.data
                                
                                if (surveys.isNotEmpty()) {
                                    showSurveySelection = true
                                    Log.d("AddJobScreen", "Found ${surveys.size} surveys")
                                } else {
                                    errorMessage = "No surveys found for the selected pilot and drone"
                                    Log.d("AddJobScreen", "No surveys found")
                                }
                            } else {
                                val errorStream = connection.errorStream?.bufferedReader()?.use { it.readText() }
                                Log.e("AddJobScreen", "Error response: $errorStream")
                                errorMessage = "Server returned error code: $responseCode"
                            }
                        } catch (e: IOException) {
                            Log.e("AddJobScreen", "Network error", e)
                            errorMessage = when {
                                e.message?.contains("Unable to resolve host") == true -> 
                                    "Cannot connect to server. Please check your internet connection and try again."
                                e.message?.contains("timeout") == true ->
                                    "Connection timed out. Please check your internet connection and try again."
                                e.message?.contains("Connection refused") == true ->
                                    "Server connection refused. Please try again later."
                                else -> "Network error: ${e.message}. Please check your internet connection."
                            }
                        } finally {
                            connection.disconnect()
                        }
                    } catch (e: Exception) {
                        Log.e("AddJobScreen", "Error creating connection", e)
                        errorMessage = "Error connecting to server: ${e.message}. Please check your internet connection."
                    }
                }
            } catch (e: Exception) {
                Log.e("AddJobScreen", "Error fetching surveys", e)
                errorMessage = "Error fetching surveys: ${e.message}"
            } finally {
                isLoading = false
            }
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Pilot Information") },
                navigationIcon = {
                    IconButton(onClick = onNavigateToSurveys) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "Back to Surveys")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "Pilot Information",
                style = MaterialTheme.typography.headlineMedium,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            OutlinedTextField(
                value = pilotName,
                onValueChange = { 
                    pilotName = it
                    showError = false
                    surveys = emptyList()
                    showSurveySelection = false
                },
                label = { Text("Pilot Name") },
                isError = showError && pilotName.isBlank(),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            )

            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = it },
            ) {
                OutlinedTextField(
                    value = droneName,
                    onValueChange = { 
                        droneName = it
                        showError = false
                        surveys = emptyList()
                        showSurveySelection = false
                    },
                    label = { Text("Drone Name") },
                    isError = showError && droneName.isBlank(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor()
                        .padding(bottom = 16.dp),
                    readOnly = true,
                    trailingIcon = {
                        ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
                    }
                )
                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    droneOptions.forEach { option ->
                        DropdownMenuItem(
                            text = { Text(option) },
                            onClick = {
                                droneName = option
                                expanded = false
                                showError = false
                                surveys = emptyList()
                                showSurveySelection = false
                            }
                        )
                    }
                }
            }

            Button(
                onClick = {
                    if (pilotName.isBlank() || droneName.isBlank()) {
                        showError = true
                        errorMessage = "Please enter both pilot and drone name."
                    } else {
                        fetchSurveys()
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                enabled = !isLoading && !isSubmitting
            ) {
                Text("Fetch Surveys")
            }

            if (errorMessage != null) {
                Text(
                    text = errorMessage!!,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
            }

            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.padding(16.dp)
                )
            }

            if (showSurveySelection) {
                Text(
                    text = "Select a Survey",
                    style = MaterialTheme.typography.titleLarge,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                surveys.forEach { survey ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp)
                            .clickable {
                                // Check if a job with this survey ID already exists before showing checklist
                                val existingJob = counterViewModel.allJobs.value.find { job ->
                                    job.surveyId == survey.id?.toString()
                                }
                                
                                if (existingJob != null) {
                                    // Job already exists, show error message
                                    errorMessage = "This survey has already been added as a job. Please select a different survey."
                                    return@clickable
                                }
                                
                                selectedSurveyForChecklist = survey
                                showPreDriveChecklist = true
                            }
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(text = survey.property_name ?: "Unnamed Survey", style = MaterialTheme.typography.titleMedium)
                            Text(text = survey.coordinates ?: "No coordinates", style = MaterialTheme.typography.bodyMedium)
                            Text(text = survey.scheduled_time ?: "No date", style = MaterialTheme.typography.bodySmall)
                        }
                    }
                }
            }
        }
    }

    if (showPreDriveChecklist && selectedSurveyForChecklist != null) {
        PreDriveChecklistDialog(
            onDismiss = { showPreDriveChecklist = false },
            onComplete = {
                showPreDriveChecklist = false
                selectedSurveyForChecklist?.let { survey ->
                    Log.d("AddJobScreen", "Creating new job from survey: ${survey.id}")
                    val newJob = Job(
                        name = survey.property_name ?: "Job ${survey.id ?: "New"}",
                        coordinates = survey.coordinates ?: "",
                        gateCode = survey.gate_code ?: "",
                        additionalNotes = survey.notes ?: "",
                        vehicleRequirements = survey.driving_instructions ?: "",
                        mapImage = survey.image?.let { 
                            val byteArray = ByteArray(it.data.size) { index ->
                                it.data[index].toByte()
                            }
                            android.util.Base64.encodeToString(byteArray, android.util.Base64.DEFAULT)
                        } ?: "",
                        mapPdf = survey.pdf?.let { 
                            val byteArray = ByteArray(it.data.size) { index ->
                                it.data[index].toByte()
                            }
                            android.util.Base64.encodeToString(byteArray, android.util.Base64.DEFAULT)
                        } ?: "",
                        pilotInfo = PilotInfo(pilotName, droneName),
                        googleSheetsId = survey.google_sheets_id?.trim(),
                        surveyId = survey.id?.toString(),
                        apiDateTime = survey.scheduled_time
                    )
                    Log.d("AddJobScreen", "New job created with ID: ${newJob.id}")
                    
                    // Set pilot info and selected job after creating the job
                    viewModel.setPilotInfo(newJob.id, pilotName, droneName)
                    viewModel.setSelectedJob(newJob)
                    
                    Log.d("AddJobScreen", "Adding job to CounterViewModel")
                    counterViewModel.addJob(newJob)
                    Log.d("AddJobScreen", "Selecting job in CounterViewModel")
                    // Note: selectJob is called within addJob with skipDataStoreLoad=true
                    Log.d("AddJobScreen", "Setting exported to false")
                    counterViewModel.setExportedToFalse()
                    
                    // Mark pre-drive checklist as completed for the new job
                    counterViewModel.markChecklistCompletedForCurrentJob("pre_drive")
                    
                    Log.d("AddJobScreen", "Navigating to counter screen")
                    onFormComplete()
                }
                selectedSurveyForChecklist = null
            },
            jobViewModel = viewModel,
            counterViewModel = counterViewModel,
            surveyForPreview = selectedSurveyForChecklist // Pass the survey for preview
        )
    }
} 