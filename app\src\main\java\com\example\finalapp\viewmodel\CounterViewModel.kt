package com.example.finalapp.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.finalapp.data.AppDataStore
import com.example.finalapp.model.ChecklistData
import com.example.finalapp.model.Counter
import com.example.finalapp.model.CounterChange
import com.example.finalapp.model.Pasture
import com.example.finalapp.model.SurveyResponse
import com.example.finalapp.model.Job
import com.example.finalapp.service.GoogleSheetsService
import com.example.finalapp.service.ApiService
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

import java.util.Date
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.TimeZone

class CounterViewModel(application: Application) : AndroidViewModel(application) {
    private val googleSheetsService = GoogleSheetsService(application)
    internal val dataStore = AppDataStore(application)

    // Add mutex for thread-safe counter updates
    private val counterUpdateMutex = Mutex()

    private val _activeJobs = MutableStateFlow<List<Job>>(emptyList())
    val activeJobs: StateFlow<List<Job>> = _activeJobs.asStateFlow()

    private val _selectedJob = MutableStateFlow<Job?>(null)
    val selectedJob: StateFlow<Job?> = _selectedJob.asStateFlow()

    // Store pastures per job ID
    private val _jobPastures = MutableStateFlow<Map<String, List<Pasture>>>(emptyMap())
    val jobPastures: StateFlow<Map<String, List<Pasture>>> = _jobPastures.asStateFlow()

    // Get pastures for current job
    val pastures: StateFlow<List<Pasture>> = combine(_selectedJob, _jobPastures) { job, pastures ->
        job?.let { pastures[it.id] } ?: emptyList()
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )

    private val _selectedPastureIndex = MutableStateFlow<Int?>(null)
    val selectedPastureIndex: StateFlow<Int?> = _selectedPastureIndex.asStateFlow()

    private val _history = MutableStateFlow<Map<String, List<CounterChange>>>(emptyMap())
    val history: StateFlow<Map<String, List<CounterChange>>> = _history.asStateFlow()

    private val _isSurveyStarted = MutableStateFlow(false)
    val isSurveyStarted: StateFlow<Boolean> = _isSurveyStarted.asStateFlow()

    private val _isExported = MutableStateFlow(false)
    val isExported: StateFlow<Boolean> = _isExported.asStateFlow()

    private val _surveyResponse = MutableStateFlow<SurveyResponse?>(null)
    val surveyResponse: StateFlow<SurveyResponse?> = _surveyResponse.asStateFlow()

    private val _isNewJob = MutableStateFlow(true)
    val isNewJob: StateFlow<Boolean> = _isNewJob.asStateFlow()

    private val _completedJobs = MutableStateFlow<List<Job>>(emptyList())
    val completedJobs: StateFlow<List<Job>> = _completedJobs.asStateFlow()

    private val _allJobs = MutableStateFlow<List<Job>>(emptyList())
    val allJobs: StateFlow<List<Job>> = _allJobs.asStateFlow()

    private val _jobsWithCompletedSurveys = MutableStateFlow<Set<String>>(emptySet())
    val jobsWithCompletedSurveys: StateFlow<Set<String>> = _jobsWithCompletedSurveys.asStateFlow()

    private val _checklistCompletion = MutableStateFlow<Map<String, Set<String>>>(emptyMap())
    val checklistCompletion: StateFlow<Map<String, Set<String>>> = _checklistCompletion.asStateFlow()

    private val _isAddingNewJob = MutableStateFlow(false)
    val isAddingNewJob: StateFlow<Boolean> = _isAddingNewJob.asStateFlow()

    private val _showPostFlightSurvey = MutableStateFlow(false)
    val showPostFlightSurvey: StateFlow<Boolean> = _showPostFlightSurvey.asStateFlow()

    // Expose job survey states for navigation logic
    val jobSurveyStates: StateFlow<Map<String, Pair<Boolean, Boolean>>> = dataStore.jobSurveyStates.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyMap()
    )

    private val apiService = ApiService()
    private val spreadsheetId = "1IZvxjeEMN-xIlatKcHmhP4TvDF1L93vhHEO7TMekB0I"

    // Performance optimization: Cache for frequently accessed data
    private val _cachedPastureCounts = MutableStateFlow<Map<String, Int>>(emptyMap())
    val cachedPastureCounts: StateFlow<Map<String, Int>> = _cachedPastureCounts.asStateFlow()

    init {
        // Load saved state
        viewModelScope.launch {
            // Load active jobs
            dataStore.activeJobs.collect { savedJobs ->
                _activeJobs.value = savedJobs
                _allJobs.value = savedJobs
                
                // If there are no active jobs, clean up all data
                if (savedJobs.isEmpty()) {
                    Log.d("CounterViewModel", "No active jobs found, cleaning up all data")
                    dataStore.clearAllData()
                }
            }
        }

        viewModelScope.launch {
            // Load completed jobs
            dataStore.completedJobs.collect { savedJobs ->
                _completedJobs.value = savedJobs
                _allJobs.value = _activeJobs.value + savedJobs
            }
        }

        viewModelScope.launch {
            // Load jobs with completed surveys
            dataStore.jobsWithCompletedSurveys.collect { savedJobIds ->
                _jobsWithCompletedSurveys.value = savedJobIds
            }
        }

        viewModelScope.launch {
            // Load selected job and its data
            dataStore.selectedJob.collect { savedJob ->
                Log.d("CounterViewModel", "DataStore selectedJob flow triggered")
                if (savedJob != null && savedJob.id != _selectedJob.value?.id) {
                    // Only load history when switching to a different job
                    val jobHistory = dataStore.jobHistory.first()
                    Log.d("CounterViewModel", "Loaded history for job ${savedJob.id}")
                    Log.d("CounterViewModel", "Loaded history size: ${jobHistory.size}")
                    Log.d("CounterViewModel", "Loaded history keys: ${jobHistory.keys}")
                    
                    // Filter history for current job and remove job ID prefix
                    val filteredHistory = jobHistory.filterKeys { it.startsWith("${savedJob.id}_") }
                        .mapKeys { it.key.substringAfter("${savedJob.id}_") }
                    Log.d("CounterViewModel", "Filtered history size: ${filteredHistory.size}")
                    Log.d("CounterViewModel", "Filtered history keys: ${filteredHistory.keys}")
                    _history.value = filteredHistory
                }
                _selectedJob.value = savedJob
                savedJob?.let { job ->
                    // Load job's pastures
                    val jobPastures = dataStore.jobPastures.first()
                    val loadedPastures = jobPastures[job.id] ?: emptyList()
                    Log.d("CounterViewModel", "Loaded ${loadedPastures.size} pastures")
                    
                    // Update pastures in memory
                    _jobPastures.update { currentMap ->
                        currentMap + (job.id to loadedPastures)
                    }

                    // Validate and adjust selected pasture index
                    val currentSelectedIndex = _selectedPastureIndex.value
                    if (currentSelectedIndex != null && currentSelectedIndex !in loadedPastures.indices) {
                        Log.w("CounterViewModel", "Selected pasture index $currentSelectedIndex is invalid for ${loadedPastures.size} pastures, resetting")
                        val validIndex = if (loadedPastures.isNotEmpty()) 0 else null
                        _selectedPastureIndex.value = validIndex
                        viewModelScope.launch {
                            dataStore.saveSelectedPastureIndex(validIndex ?: -1)
                        }
                    }
                    
                    // Load job's survey response
                    val jobSurveyResponse = dataStore.jobSurveyResponses.first()[job.id]
                    _surveyResponse.value = jobSurveyResponse
                    
                    // Only load survey state if we're not adding a new job
                    if (!_isAddingNewJob.value) {
                        // Load job's survey state
                        val jobSurveyStates = dataStore.jobSurveyStates.first()
                        val (isStarted, isExported) = jobSurveyStates[job.id] ?: (false to false)
                        Log.d("CounterViewModel", "Loading survey state for job ${job.id} - isStarted: $isStarted, isExported: $isExported")
                        _isSurveyStarted.value = isStarted && !isExported
                        _isExported.value = isExported
                    } else {
                        Log.d("CounterViewModel", "Skipping survey state load for new job ${job.id}")
                    }
                }
            }
        }

        viewModelScope.launch {
            // Load selected pasture index
            dataStore.selectedPastureIndex.collect { savedIndex ->
                _selectedPastureIndex.value = savedIndex
            }
        }

        viewModelScope.launch {
            // Load new job state
            dataStore.isNewJob.collect { isNew ->
                _isNewJob.value = isNew
            }
        }

        viewModelScope.launch {
            // Load checklist completion
            dataStore.jobChecklistCompletion.collect { completionMap ->
                _checklistCompletion.value = completionMap
            }
        }

        viewModelScope.launch {
            // Load showPostFlightSurvey state for current job
            combine(dataStore.showPostFlightSurvey, _selectedJob) { map, job ->
                job?.let { map[it.id] ?: false } ?: false
            }.collect { value ->
                _showPostFlightSurvey.value = value
            }
        }

        // Update cached pasture counts when pastures change
        viewModelScope.launch {
            pastures.collect { pastureList ->
                _cachedPastureCounts.value = pastureList.associate { pasture ->
                    pasture.name to pasture.counters.sumOf { it.value }
                }
            }
        }
    }

    fun addJob(job: Job) {
        Log.d("CounterViewModel", "=== Adding new job ${job.id} ===")
        viewModelScope.launch {
            // Set flag to prevent DataStore loading from overriding initial state
            _isAddingNewJob.value = true
            
            // Add to active jobs
            val updatedActiveJobs = _activeJobs.value.toMutableList()
            if (!updatedActiveJobs.any { it.id == job.id }) {
                Log.d("CounterViewModel", "Adding job to active jobs")
                updatedActiveJobs.add(job)
                _activeJobs.value = updatedActiveJobs
                dataStore.saveActiveJobs(updatedActiveJobs)
            } else {
                Log.d("CounterViewModel", "Job already exists in active jobs")
            }
            
            // Add to all jobs
            val updatedAllJobs = _allJobs.value.toMutableList()
            if (!updatedAllJobs.any { it.id == job.id }) {
                updatedAllJobs.add(job)
                _allJobs.value = updatedAllJobs
            }
            
            // Initialize empty data for the new job
            _jobPastures.update { currentMap ->
                currentMap + (job.id to emptyList())
            }
            dataStore.saveJobPastures(_jobPastures.value)
            
            // Initialize survey state for the new job
            _isSurveyStarted.value = false
            _isExported.value = false
            _surveyResponse.value = null
            dataStore.saveJobSurveyState(job.id, false, false)
            
            // Select the newly added job - but skip the DataStore loading for new jobs
            selectJob(job, skipDataStoreLoad = true)
            
            // Clear the flag after a short delay to allow the DataStore operations to complete
            kotlinx.coroutines.delay(100)
            _isAddingNewJob.value = false
        }
    }

    fun selectJob(job: Job?, skipDataStoreLoad: Boolean = false) {
        viewModelScope.launch {
            if (job == null) {
                // Going back to surveys page - save current job data first
                Log.d("CounterViewModel", "=== Exiting current job ===")
                saveCurrentJobData()
                
                // Clear current job selection
                _selectedJob.value = null
                dataStore.saveSelectedJob(null)
                
                // Reset state
                _selectedPastureIndex.value = null
                _history.value = emptyMap()
                _isSurveyStarted.value = false
                _isExported.value = false
                _surveyResponse.value = null
                
                return@launch
            }

            Log.d("CounterViewModel", "=== Selecting job ${job.id} ===")
            
            // Switch to new job
            _selectedJob.value = job
            dataStore.saveSelectedJob(job)
            
            if (skipDataStoreLoad) {
                // For new jobs, skip loading from DataStore to preserve initial state
                Log.d("CounterViewModel", "Skipping DataStore load for new job ${job.id}")
                _selectedPastureIndex.value = null
                dataStore.saveSelectedPastureIndex(null)
                
                // Log final state
                Log.d("CounterViewModel", "=== Job ${job.id} loaded (new job) ===")
                Log.d("CounterViewModel", "Pastures: ${_jobPastures.value[job.id]?.size ?: 0}")
                Log.d("CounterViewModel", "History entries: ${_history.value.size}")
                Log.d("CounterViewModel", "Survey started: ${_isSurveyStarted.value}")
                return@launch
            }
            
            // Load new job's data
            Log.d("CounterViewModel", "Loading data for job ${job.id}")
            
            // Load pastures from DataStore
            val allJobPastures = dataStore.jobPastures.first()
            val loadedPastures = allJobPastures[job.id] ?: emptyList()
            Log.d("CounterViewModel", "Loaded ${loadedPastures.size} pastures")
            
            // Update pastures in memory
            _jobPastures.update { currentMap ->
                currentMap + (job.id to loadedPastures)
            }
            
            // Load job's survey response
            val jobSurveyResponse = dataStore.jobSurveyResponses.first()[job.id]
            Log.d("CounterViewModel", "Loading survey response for job ${job.id}")
            Log.d("CounterViewModel", "Survey response: $jobSurveyResponse")
            if (jobSurveyResponse != null) {
                Log.d("CounterViewModel", "Survey response startTimestamp: ${jobSurveyResponse.startTimestamp}")
                Log.d("CounterViewModel", "Survey response completionTimestamp: ${jobSurveyResponse.completionTimestamp}")
            }
            _surveyResponse.value = jobSurveyResponse
            
            // Load job's history
            val jobHistory = dataStore.jobHistory.first()
            Log.d("CounterViewModel", "Raw job history size: ${jobHistory.size}")
            Log.d("CounterViewModel", "Raw job history keys: ${jobHistory.keys}")
            
            val filteredHistory = jobHistory.filterKeys { it.startsWith("${job.id}_") }
                .mapKeys { it.key.substringAfter("${job.id}_") }
            Log.d("CounterViewModel", "Loaded history for ${filteredHistory.size} pastures")
            Log.d("CounterViewModel", "Filtered history keys: ${filteredHistory.keys}")
            filteredHistory.forEach { (pastureName, changes) ->
                Log.d("CounterViewModel", "Pasture '$pastureName' has ${changes.size} changes")
            }
            _history.value = filteredHistory
            
            // Load job's survey state
            val jobSurveyStates = dataStore.jobSurveyStates.first()
            val (isStarted, isExported) = jobSurveyStates[job.id] ?: (false to false)
            _isSurveyStarted.value = isStarted && !isExported
            _isExported.value = isExported
            
            // Reset pasture selection
            _selectedPastureIndex.value = null
            dataStore.saveSelectedPastureIndex(null)
            
            // Log final state
            Log.d("CounterViewModel", "=== Job ${job.id} loaded ===")
            Log.d("CounterViewModel", "Pastures: ${_jobPastures.value[job.id]?.size ?: 0}")
            Log.d("CounterViewModel", "History entries: ${_history.value.size}")
            Log.d("CounterViewModel", "Survey started: ${_isSurveyStarted.value}")
        }
    }

    fun updateJobStatus(job: Job, newStatus: String) {
        viewModelScope.launch {
            _activeJobs.update { currentList ->
                currentList.map { 
                    if (it.id == job.id) it.copy(status = newStatus) else it 
                }
            }
            dataStore.saveActiveJobs(_activeJobs.value)
        }
    }

    fun addPasture(name: String, temperature: Double): Boolean {
        val jobId = _selectedJob.value?.id ?: return false
        val currentPastures = _jobPastures.value[jobId] ?: emptyList()

        // Check for duplicate names (case-insensitive)
        if (currentPastures.any { it.name.equals(name, ignoreCase = true) }) {
            return false
        }

        viewModelScope.launch {
            counterUpdateMutex.withLock {
                val newPasture = Pasture(name = name, temperature = temperature)
                _jobPastures.update { currentMap ->
                    currentMap + (jobId to (currentPastures + newPasture))
                }
                if (_selectedPastureIndex.value == null) {
                    _selectedPastureIndex.value = 0
                }
                dataStore.saveJobPastures(_jobPastures.value)
            }
        }
        return true
    }

    fun selectPasture(index: Int) {
        val currentPastures = pastures.value
        if (index == -1) {
            // Clear selection
            _selectedPastureIndex.value = null
            viewModelScope.launch {
                dataStore.saveSelectedPastureIndex(-1)
            }
            Log.d("CounterViewModel", "Cleared pasture selection")
        } else if (index in currentPastures.indices) {
            _selectedPastureIndex.value = index
            viewModelScope.launch {
                dataStore.saveSelectedPastureIndex(index)
            }
            Log.d("CounterViewModel", "Selected pasture at index: $index")
        } else {
            Log.w("CounterViewModel", "Attempted to select invalid pasture index: $index (pastures size: ${currentPastures.size})")
            // Reset to a valid index or null
            val validIndex = if (currentPastures.isNotEmpty()) 0 else null
            _selectedPastureIndex.value = validIndex
            viewModelScope.launch {
                dataStore.saveSelectedPastureIndex(validIndex ?: -1)
            }
        }
    }

    fun deletePasture(index: Int) {
        viewModelScope.launch {
            counterUpdateMutex.withLock {
                val jobId = _selectedJob.value?.id ?: return@withLock
                val currentPastures = _jobPastures.value[jobId] ?: emptyList()

                if (index in currentPastures.indices) {
                    val pastureToDelete = currentPastures[index]
                    val pastureNameToDelete = pastureToDelete.name

                    // Remove pasture from the list
                    _jobPastures.update { currentMap ->
                        val updatedPastures = currentPastures.toMutableList().apply {
                            removeAt(index)
                        }

                        // Adjust selected pasture index if necessary
                        val currentSelectedIndex = _selectedPastureIndex.value
                        when {
                            updatedPastures.isEmpty() -> _selectedPastureIndex.value = null
                            currentSelectedIndex == index -> {
                                // If we deleted the selected pasture, select the previous one or first one
                                _selectedPastureIndex.value = if (index > 0) index - 1 else 0
                            }
                            currentSelectedIndex != null && currentSelectedIndex > index -> {
                                // If we deleted a pasture before the selected one, adjust the index
                                _selectedPastureIndex.value = currentSelectedIndex - 1
                            }
                        }

                        currentMap + (jobId to updatedPastures)
                    }

                    // Clear history for the deleted pasture from local state
                    _history.update { currentHistory ->
                        currentHistory - pastureNameToDelete
                    }

                    // Save updated pastures
                    dataStore.saveJobPastures(_jobPastures.value)

                    // Clean up persistent history data for the deleted pasture
                    dataStore.deletePastureHistory(jobId, pastureNameToDelete)

                    // Save updated local history state
                    dataStore.saveJobHistory(jobId, _history.value)

                    // Save selected pasture index
                    _selectedPastureIndex.value?.let { selectedIndex ->
                        dataStore.saveSelectedPastureIndex(selectedIndex)
                    }

                    Log.d("CounterViewModel", "Deleted pasture: $pastureNameToDelete and cleaned up history")
                }
            }
        }
    }

    fun addCounter(name: String) {
        viewModelScope.launch {
            counterUpdateMutex.withLock {
                val jobId = _selectedJob.value?.id ?: return@withLock
                val pastureIndex = _selectedPastureIndex.value ?: return@withLock
                val currentPasture = pastures.value.getOrNull(pastureIndex) ?: return@withLock
                
                val newCounter = Counter(name = name)
                
                _jobPastures.update { currentMap ->
                    val currentPastures = currentMap[jobId] ?: emptyList()
                    val updatedPastures = currentPastures.toMutableList().apply {
                        val pasture = this[pastureIndex]
                        this[pastureIndex] = pasture.copy(
                            counters = pasture.counters + newCounter
                        )
                    }
                    currentMap + (jobId to updatedPastures)
                }
                dataStore.saveJobPastures(_jobPastures.value)
            }
        }
    }

    // Thread-safe updateCounter method with mutex protection
    fun updateCounter(index: Int, newValue: Int) {
        viewModelScope.launch {
            counterUpdateMutex.withLock {
                val jobId = _selectedJob.value?.id ?: return@withLock
                val pastureIndex = _selectedPastureIndex.value ?: return@withLock
                
                // Get current state atomically
                val currentPastures = _jobPastures.value[jobId] ?: return@withLock
                if (pastureIndex !in currentPastures.indices) return@withLock
                
                val pasture = currentPastures[pastureIndex]
                if (index !in pasture.counters.indices) return@withLock
                
                val oldValue = pasture.counters[index].value
                if (oldValue == newValue) return@withLock // No change needed
                
                val changeAmount = newValue - oldValue
                
                // Update state atomically
                val updatedCounters = pasture.counters.toMutableList().apply {
                    this[index] = this[index].copy(value = newValue)
                }
                val updatedPasture = pasture.copy(counters = updatedCounters)
                _jobPastures.update { currentMap ->
                    val updatedPastures = currentMap[jobId]?.toMutableList()?.apply {
                        this[pastureIndex] = updatedPasture
                    } ?: currentMap[jobId] ?: emptyList()
                    currentMap + (jobId to updatedPastures)
                }
                
                // Only create history entry if there was a change
                if (changeAmount != 0) {
                    val change = CounterChange(
                        timestamp = System.currentTimeMillis(),
                        counterName = pasture.counters[index].name,
                        changeAmount = changeAmount
                    )
                    _history.update { currentHistory ->
                        val pastureHistory = currentHistory[pasture.name] ?: emptyList()
                        val updatedPastureHistory = pastureHistory + change
                        currentHistory + (pasture.name to updatedPastureHistory)
                    }
                }
                
                // Save both pastures and history to DataStore
                dataStore.saveJobPastures(_jobPastures.value)
                _selectedJob.value?.let { job ->
                    dataStore.saveJobHistory(job.id, _history.value)
                }
            }
        }
    }

    fun removeCounter(index: Int) {
        viewModelScope.launch {
            counterUpdateMutex.withLock {
                val jobId = _selectedJob.value?.id ?: return@withLock
                val pastureIndex = _selectedPastureIndex.value ?: return@withLock
                _jobPastures.update { currentMap ->
                    val currentPastures = currentMap[jobId] ?: emptyList()
                    if (pastureIndex in currentPastures.indices) {
                        val pasture = currentPastures[pastureIndex]
                        val counterToDelete = pasture.counters.getOrNull(index)
                        val updatedCounters = pasture.counters.toMutableList().apply {
                            if (index in indices) {
                                removeAt(index)
                            }
                        }
                        val updatedPastures = currentPastures.toMutableList().apply {
                            this[pastureIndex] = pasture.copy(counters = updatedCounters)
                        }

                        // Clean up history data for the deleted counter
                        counterToDelete?.let { deletedCounter ->
                            _history.update { currentHistory ->
                                val pastureHistory = currentHistory[pasture.name] ?: emptyList()
                                val filteredHistory = pastureHistory.filter { change ->
                                    change.counterName != deletedCounter.name
                                }
                                if (filteredHistory.isEmpty()) {
                                    currentHistory - pasture.name
                                } else {
                                    currentHistory + (pasture.name to filteredHistory)
                                }
                            }

                            // Save updated history to DataStore
                            dataStore.saveJobHistory(jobId, _history.value)

                            Log.d("CounterViewModel", "Deleted counter '${deletedCounter.name}' and cleaned up its history")
                        }

                        currentMap + (jobId to updatedPastures)
                    } else {
                        currentMap
                    }
                }
                dataStore.saveJobPastures(_jobPastures.value)
            }
        }
    }

    fun reorderCounter(fromIndex: Int, toIndex: Int) {
        viewModelScope.launch {
            counterUpdateMutex.withLock {
                val jobId = _selectedJob.value?.id ?: return@withLock
                val pastureIndex = _selectedPastureIndex.value ?: return@withLock
                _jobPastures.update { currentMap ->
                    val currentPastures = currentMap[jobId] ?: emptyList()
                    if (pastureIndex in currentPastures.indices) {
                        val pasture = currentPastures[pastureIndex]
                        val updatedCounters = pasture.counters.toMutableList().apply {
                            if (fromIndex in indices && toIndex in indices) {
                                val item = removeAt(fromIndex)
                                add(toIndex, item)
                            }
                        }
                        val updatedPastures = currentPastures.toMutableList().apply {
                            this[pastureIndex] = pasture.copy(counters = updatedCounters)
                        }
                        currentMap + (jobId to updatedPastures)
                    } else {
                        currentMap
                    }
                }
                dataStore.saveJobPastures(_jobPastures.value)
            }
        }
    }

    fun startSurvey() {
        Log.d("CounterViewModel", "Starting survey")
        _isSurveyStarted.value = true
        // Set the start timestamp only if not already set
        if (_surveyResponse.value == null) {
            val startTimestamp = System.currentTimeMillis()
            Log.d("CounterViewModel", "Creating new survey response with startTimestamp: $startTimestamp")
            val newSurveyResponse = SurveyResponse(
                topography = "",
                cover = "",
                animalDensity = "",
                dayWeather = "",
                missionTypes = emptyList(),
                takeOffsAndLandings = 0,
                additionalNotes = "",
                remarksProceduresManeuvers = "",
                batteryChanges = 0,
                startTimestamp = startTimestamp,
                completionTimestamp = 0L,
                isNight = false
            )
            _surveyResponse.value = newSurveyResponse

            // Save the survey response immediately when it's created
            viewModelScope.launch {
                _selectedJob.value?.let { job ->
                    Log.d("CounterViewModel", "Saving initial survey response for job ${job.id}")
                    dataStore.saveJobSurveyResponse(job.id, newSurveyResponse)
                }
            }
        } else {
            Log.d("CounterViewModel", "Survey response already exists, startTimestamp: ${_surveyResponse.value?.startTimestamp}")
        }
        viewModelScope.launch {
            _selectedJob.value?.let { job ->
                dataStore.saveJobSurveyState(job.id, true, _isExported.value)
            }
        }
    }

    fun setFlightCompletionTimestamp() {
        val currentResponse = _surveyResponse.value
        if (currentResponse != null && currentResponse.completionTimestamp == 0L) {
            val completionTimestamp = System.currentTimeMillis()
            Log.d("CounterViewModel", "Setting flight completion timestamp: $completionTimestamp")
            Log.d("CounterViewModel", "Flight duration: ${completionTimestamp - currentResponse.startTimestamp} ms")

            val updatedResponse = currentResponse.copy(completionTimestamp = completionTimestamp)
            _surveyResponse.value = updatedResponse

            // Save the updated survey response
            viewModelScope.launch {
                _selectedJob.value?.let { job ->
                    Log.d("CounterViewModel", "Saving survey response with completion timestamp for job ${job.id}")
                    dataStore.saveJobSurveyResponse(job.id, updatedResponse)
                }
            }
        } else if (currentResponse?.completionTimestamp != 0L) {
            Log.d("CounterViewModel", "Completion timestamp already set: ${currentResponse?.completionTimestamp}")
        } else {
            Log.w("CounterViewModel", "No survey response found when trying to set completion timestamp")
        }
    }

    fun finishSurvey() {
        Log.d("CounterViewModel", "Finishing survey")
        _isSurveyStarted.value = false
        viewModelScope.launch {
            _selectedJob.value?.let { job ->
                dataStore.saveJobSurveyState(job.id, false, _isExported.value)
            }
        }
    }

    fun setExportedToFalse() {
        Log.d("CounterViewModel", "Setting exported to false")
        _isExported.value = false
        viewModelScope.launch {
            _selectedJob.value?.let { job ->
                dataStore.saveJobSurveyState(job.id, _isSurveyStarted.value, false)
            }
        }
    }

    fun setExportedToTrue() {
        Log.d("CounterViewModel", "Setting exported to true")
        _isExported.value = true
        viewModelScope.launch {
            _selectedJob.value?.let { job ->
                dataStore.saveJobSurveyState(job.id, _isSurveyStarted.value, true)
            }
        }
    }

    fun updateSurveyResponse(response: SurveyResponse) {
        val jobId = _selectedJob.value?.id ?: return
        Log.d("CounterViewModel", "Updating survey response for job $jobId")
        Log.d("CounterViewModel", "Survey response startTimestamp: ${response.startTimestamp}")
        Log.d("CounterViewModel", "Survey response completionTimestamp: ${response.completionTimestamp}")
        _surveyResponse.value = response
        viewModelScope.launch {
            dataStore.saveJobSurveyResponse(jobId, response)
        }
    }

    suspend fun updateClientNotes(clientNotes: String) {
        val jobId = _selectedJob.value?.id ?: return
        Log.d("CounterViewModel", "=== CLIENT NOTES UPDATE START ===")
        Log.d("CounterViewModel", "Updating client notes for job $jobId: '$clientNotes'")

        val checklistData = ChecklistData(clientNotes = clientNotes)
        dataStore.saveJobChecklistData(jobId, checklistData)
        Log.d("CounterViewModel", "Successfully saved checklist data with client notes")

        // Verify the data was saved by immediately reading it back
        val savedData = dataStore.jobChecklistData.first()[jobId]
        Log.d("CounterViewModel", "Verification - saved checklist data: $savedData")
        if (savedData != null) {
            Log.d("CounterViewModel", "Verification - client notes in saved data: '${savedData.clientNotes}'")
        } else {
            Log.e("CounterViewModel", "ERROR: No checklist data found immediately after saving!")
        }
        Log.d("CounterViewModel", "=== CLIENT NOTES UPDATE END ===")
    }

    fun exportJobToGoogleSheets(job: Job, onComplete: (Boolean) -> Unit) {
        if (!_jobsWithCompletedSurveys.value.contains(job.id)) {
            onComplete(false)
            return
        }

        viewModelScope.launch {
            try {
                Log.d("CounterViewModel", "=== EXPORT PROCESS START ===")
                Log.d("CounterViewModel", "Exporting job ${job.id} (${job.name})")

                // IMMEDIATELY check what client notes data exists before doing anything else
                val allChecklistDataAtStart = dataStore.jobChecklistData.first()
                Log.d("CounterViewModel", "EXPORT START - All checklist data keys: ${allChecklistDataAtStart.keys}")
                val jobChecklistDataAtStart = allChecklistDataAtStart[job.id]
                Log.d("CounterViewModel", "EXPORT START - Checklist data for job ${job.id}: $jobChecklistDataAtStart")
                if (jobChecklistDataAtStart != null) {
                    Log.d("CounterViewModel", "EXPORT START - Client notes found: '${jobChecklistDataAtStart.clientNotes}'")
                } else {
                    Log.e("CounterViewModel", "EXPORT START - ERROR: No checklist data found for job ${job.id}")
                }

                // Get all job data before attempting export
                val jobPastures = _jobPastures.value[job.id] ?: emptyList()
                val jobHistory = _history.value

                Log.d("CounterViewModel", "Job pastures: ${jobPastures.size}")
                Log.d("CounterViewModel", "Job history entries: ${jobHistory.size}")
                jobHistory.forEach { (pastureName, changes) ->
                    Log.d("CounterViewModel", "Pasture '$pastureName' has ${changes.size} changes")
                }
                
                val jobSurveyResponse = dataStore.jobSurveyResponses.first()[job.id]
                val allChecklistData = dataStore.jobChecklistData.first()
                val jobChecklistData = allChecklistData[job.id]

                Log.d("CounterViewModel", "Retrieved survey response for export: $jobSurveyResponse")
                Log.d("CounterViewModel", "All checklist data keys: ${allChecklistData.keys}")
                Log.d("CounterViewModel", "Looking for job ID: ${job.id}")
                Log.d("CounterViewModel", "Retrieved checklist data for export: $jobChecklistData")
                if (jobChecklistData != null) {
                    Log.d("CounterViewModel", "Client notes from checklist: '${jobChecklistData.clientNotes}'")
                } else {
                    Log.d("CounterViewModel", "No checklist data found for job ${job.id}")
                }

                // Use survey response as-is (no client notes merging)
                val finalSurveyResponse = jobSurveyResponse

                Log.d("CounterViewModel", "Final survey response for export: $finalSurveyResponse")

                val spreadsheetId = job.googleSheetsId ?: return@launch
                Log.d("CounterViewModel", "Exporting job with spreadsheet ID: '$spreadsheetId'")
                Log.d("CounterViewModel", "Spreadsheet ID length: ${spreadsheetId.length}")
                Log.d("CounterViewModel", "Spreadsheet ID bytes: ${spreadsheetId.toByteArray().contentToString()}")

                // Final check before calling GoogleSheetsService
                Log.d("CounterViewModel", "=== FINAL CHECK BEFORE GOOGLE SHEETS SERVICE ===")
                Log.d("CounterViewModel", "Survey response: $finalSurveyResponse")
                if (jobChecklistData != null) {
                    Log.d("CounterViewModel", "Client notes from checklist: '${jobChecklistData.clientNotes}'")
                    Log.d("CounterViewModel", "Client notes length: ${jobChecklistData.clientNotes.length}")
                } else {
                    Log.e("CounterViewModel", "ERROR: No checklist data found!")
                }

                val success = googleSheetsService.exportJobData(
                    spreadsheetId = spreadsheetId,
                    job = job,
                    pastures = jobPastures,
                    history = jobHistory,
                    surveyResponse = finalSurveyResponse,
                    clientNotes = jobChecklistData?.clientNotes ?: ""
                )
                
                if (success) {
                    // After successful export, delete the job from the API
                    val pilotInfo = job.pilotInfo
                    if (pilotInfo != null && job.apiDateTime != null) {
                        Log.d("CounterViewModel", "Attempting to delete job from API: pilot=${pilotInfo.name}, drone=${pilotInfo.droneName}, datetime=${job.apiDateTime}")
                        
                        val deleteSuccess = apiService.deleteSurvey(
                            pilot = pilotInfo.name,
                            droneName = pilotInfo.droneName,
                            datetime = job.apiDateTime!!
                        )
                        
                        if (deleteSuccess) {
                            Log.d("CounterViewModel", "Successfully deleted job from API")
                        } else {
                            Log.w("CounterViewModel", "Failed to delete job from API, but export was successful")
                        }
                    } else {
                        Log.w("CounterViewModel", "No pilot info or API datetime available for job ${job.id}, skipping API delete")
                    }
                    
                    // Only clear data if export was successful
                    // Remove from jobs with completed surveys
                    val updatedSurveyJobs = _jobsWithCompletedSurveys.value - job.id
                    _jobsWithCompletedSurveys.value = updatedSurveyJobs
                    dataStore.saveJobsWithCompletedSurveys(updatedSurveyJobs)
                    
                    // Remove from active jobs
                    _activeJobs.update { currentList ->
                        currentList.filter { it.id != job.id }
                    }
                    dataStore.saveActiveJobs(_activeJobs.value)
                    
                    // Remove from all jobs
                    _allJobs.update { currentList ->
                        currentList.filter { it.id != job.id }
                    }
                    
                    // Clear all job-specific data
                    _jobPastures.update { currentMap ->
                        currentMap - job.id
                    }
                    dataStore.saveJobPastures(_jobPastures.value)
                    dataStore.saveJobSurveyResponse(job.id, null)
                    dataStore.saveJobHistory(job.id, emptyMap())
                    
                    // If this was the selected job, clear selection and reset state
                    if (_selectedJob.value?.id == job.id) {
                        _selectedJob.value = null
                        _selectedPastureIndex.value = null
                        _history.value = emptyMap()
                        _isSurveyStarted.value = false
                        _isExported.value = false
                        _surveyResponse.value = null
                        dataStore.saveSelectedJob(null)
                        dataStore.saveSelectedPastureIndex(null)
                        dataStore.saveJobSurveyState(job.id, false, false)
                    }
                } else {
                    // If export failed, keep the job in the exportable state
                    _isExported.value = false
                    dataStore.saveJobSurveyState(job.id, _isSurveyStarted.value, false)
                }
                
                onComplete(success)
            } catch (e: Exception) {
                e.printStackTrace()
                // If export failed, keep the job in the exportable state
                _isExported.value = false
                dataStore.saveJobSurveyState(job.id, _isSurveyStarted.value, false)
                onComplete(false)
            }
        }
    }

    fun exportJobToGoogleSheetsWithCustomId(job: Job, customSpreadsheetId: String, onComplete: (Boolean) -> Unit) {
        if (!_jobsWithCompletedSurveys.value.contains(job.id)) {
            onComplete(false)
            return
        }

        viewModelScope.launch {
            try {
                // Get all job data before attempting export
                val jobPastures = _jobPastures.value[job.id] ?: emptyList()
                val jobHistory = _history.value

                Log.d("CounterViewModel", "Exporting job ${job.id} with custom spreadsheet ID")
                Log.d("CounterViewModel", "Custom spreadsheet ID: '$customSpreadsheetId'")
                Log.d("CounterViewModel", "Job pastures: ${jobPastures.size}")
                Log.d("CounterViewModel", "Job history entries: ${jobHistory.size}")

                val jobSurveyResponse = dataStore.jobSurveyResponses.first()[job.id]
                val jobChecklistData = dataStore.jobChecklistData.first()[job.id]

                Log.d("CounterViewModel", "Retrieved survey response for custom export: $jobSurveyResponse")
                Log.d("CounterViewModel", "Retrieved checklist data for custom export: $jobChecklistData")
                if (jobChecklistData != null) {
                    Log.d("CounterViewModel", "Client notes from checklist: '${jobChecklistData.clientNotes}'")
                }

                // Use survey response as-is (no client notes merging)
                val finalSurveyResponse = jobSurveyResponse

                Log.d("CounterViewModel", "Final survey response for custom export: $finalSurveyResponse")

                val success = googleSheetsService.exportJobDataWithCustomId(
                    customSpreadsheetId = customSpreadsheetId,
                    job = job,
                    pastures = jobPastures,
                    history = jobHistory,
                    surveyResponse = finalSurveyResponse,
                    clientNotes = jobChecklistData?.clientNotes ?: ""
                )

                if (success) {
                    // After successful export, delete the job from the API (same as normal export)
                    val pilotInfo = job.pilotInfo
                    if (pilotInfo != null && job.apiDateTime != null) {
                        Log.d("CounterViewModel", "Attempting to delete job from API: pilot=${pilotInfo.name}, drone=${pilotInfo.droneName}, datetime=${job.apiDateTime}")

                        val deleteSuccess = apiService.deleteSurvey(
                            pilot = pilotInfo.name,
                            droneName = pilotInfo.droneName,
                            datetime = job.apiDateTime!!
                        )

                        if (deleteSuccess) {
                            Log.d("CounterViewModel", "Successfully deleted job from API")
                        } else {
                            Log.w("CounterViewModel", "Failed to delete job from API, but export was successful")
                        }
                    } else {
                        Log.w("CounterViewModel", "No pilot info or API datetime available for job ${job.id}, skipping API delete")
                    }

                    Log.d("CounterViewModel", "Export with custom ID successful for job ${job.id}")

                    // Only clear data if export was successful
                    // Remove from jobs with completed surveys
                    val updatedSurveyJobs = _jobsWithCompletedSurveys.value - job.id
                    _jobsWithCompletedSurveys.value = updatedSurveyJobs
                    dataStore.saveJobsWithCompletedSurveys(updatedSurveyJobs)

                    // Remove from active jobs
                    _activeJobs.update { currentList ->
                        currentList.filter { it.id != job.id }
                    }
                    dataStore.saveActiveJobs(_activeJobs.value)

                    // Remove from all jobs
                    _allJobs.update { currentList ->
                        currentList.filter { it.id != job.id }
                    }

                    // Clear all job-specific data
                    _jobPastures.update { currentMap ->
                        currentMap - job.id
                    }
                    dataStore.saveJobPastures(_jobPastures.value)
                    dataStore.saveJobSurveyResponse(job.id, null)
                    dataStore.saveJobHistory(job.id, emptyMap())

                    // If this was the selected job, clear selection and reset state
                    if (_selectedJob.value?.id == job.id) {
                        _selectedJob.value = null
                        _selectedPastureIndex.value = null
                        _history.value = emptyMap()
                        _isSurveyStarted.value = false
                        _isExported.value = false
                        _surveyResponse.value = null
                        dataStore.saveSelectedJob(null)
                        dataStore.saveSelectedPastureIndex(null)
                        dataStore.saveJobSurveyState(job.id, false, false)
                    }
                } else {
                    // If export failed, keep the job in the exportable state
                    _isExported.value = false
                    dataStore.saveJobSurveyState(job.id, _isSurveyStarted.value, false)
                }

                onComplete(success)
            } catch (e: Exception) {
                e.printStackTrace()
                // If export failed, keep the job in the exportable state
                _isExported.value = false
                dataStore.saveJobSurveyState(job.id, _isSurveyStarted.value, false)
                onComplete(false)
            }
        }
    }

    // Function to check if there's any existing data
    suspend fun hasExistingData(): Boolean {
        val savedPastures = dataStore.jobPastures.first()
        val jobSurveyStates = dataStore.jobSurveyStates.first()
        return savedPastures.isNotEmpty() || jobSurveyStates.isNotEmpty()
    }

    fun undoCounterChange(change: CounterChange, pastureName: String) {
        viewModelScope.launch {
            counterUpdateMutex.withLock {
                val jobId = _selectedJob.value?.id ?: return@withLock
                val pastureIndex = _selectedPastureIndex.value ?: return@withLock
                _jobPastures.update { currentMap ->
                    val currentPastures = currentMap[jobId] ?: emptyList()
                    if (pastureIndex in currentPastures.indices) {
                        val pasture = currentPastures[pastureIndex]
                        val counterIndex = pasture.counters.indexOfFirst { it.name == change.counterName }
                        if (counterIndex != -1) {
                            val updatedCounters = pasture.counters.toMutableList().apply {
                                this[counterIndex] = this[counterIndex].copy(
                                    value = this[counterIndex].value - change.changeAmount
                                )
                            }
                            val updatedPastures = currentPastures.toMutableList().apply {
                                this[pastureIndex] = pasture.copy(counters = updatedCounters)
                            }
                            currentMap + (jobId to updatedPastures)
                        } else {
                            currentMap
                        }
                    } else {
                        currentMap
                    }
                }
                
                // Remove the change from history
                _history.update { currentHistory ->
                    val pastureHistory = currentHistory[pastureName] ?: emptyList()
                    val updatedPastureHistory = pastureHistory.filter { it != change }
                    currentHistory + (pastureName to updatedPastureHistory)
                }
                
                dataStore.saveJobPastures(_jobPastures.value)
                dataStore.saveJobHistory(jobId, _history.value)
            }
        }
    }

    fun startNewJob() {
        viewModelScope.launch {
            // Clear all existing data
            clearAllData()
            // Set new job flag to true
            _isNewJob.value = true
            dataStore.saveNewJobState(true)
        }
    }

    fun finishJob() {
        _isNewJob.value = false
        viewModelScope.launch {
            dataStore.saveNewJobState(false)
            // Update the job status in active jobs
            _activeJobs.value = _activeJobs.value.map { job ->
                if (job.id == _selectedJob.value?.id) {
                    job.copy(status = "Completed")
                } else {
                    job
                }
            }
            // Save the updated active jobs
            dataStore.saveActiveJobs(_activeJobs.value)
        }
    }

    fun clearAllData() {
        viewModelScope.launch {
            dataStore.clearAllData()
            _jobPastures.value = emptyMap()
            _selectedPastureIndex.value = null
            _history.value = emptyMap()
            _isSurveyStarted.value = false
            _isExported.value = false
            _surveyResponse.value = null
            _isNewJob.value = true
        }
    }

    fun markJobSurveyCompleted(jobId: String) {
        viewModelScope.launch {
            val updatedSet = _jobsWithCompletedSurveys.value + jobId
            _jobsWithCompletedSurveys.value = updatedSet
            dataStore.saveJobsWithCompletedSurveys(updatedSet)
        }
    }

    // Optimized getPastureCounts using cached data
    fun getPastureCounts(): Map<String, Int> {
        return _cachedPastureCounts.value
    }

    fun clearJobData(jobId: String) {
        viewModelScope.launch {
            // Clear job-specific data
            _jobPastures.update { currentMap ->
                currentMap - jobId
            }
            dataStore.saveJobPastures(_jobPastures.value)
            
            // Clear job's survey response
            dataStore.saveJobSurveyResponse(jobId, null)
            
            // Clear job's history
            dataStore.saveJobHistory(jobId, emptyMap())
            _history.value = emptyMap()
            
            // Remove from active jobs
            _activeJobs.update { currentList ->
                currentList.filter { it.id != jobId }
            }
            dataStore.saveActiveJobs(_activeJobs.value)
            
            // Remove from all jobs
            _allJobs.update { currentList ->
                currentList.filter { it.id != jobId }
            }
            
            // If the cleared job was selected, clear selection
            if (_selectedJob.value?.id == jobId) {
                _selectedJob.value = null
                _selectedPastureIndex.value = null
                _history.value = emptyMap()
                _isSurveyStarted.value = false
                _isExported.value = false
                _surveyResponse.value = null
                dataStore.saveSelectedJob(null)
                dataStore.saveSelectedPastureIndex(null)
                dataStore.saveJobSurveyState(jobId, false, false)
            }
        }
    }

    // Add this function to be called from the activity when it's being destroyed
    fun onAppClose() {
        Log.d("CounterViewModel", "onAppClose called")
        viewModelScope.launch {
            try {
                Log.d("CounterViewModel", "Starting to save data in onAppClose")
                saveCurrentJobData()
                Log.d("CounterViewModel", "Successfully saved data in onAppClose")
            } catch (e: Exception) {
                Log.e("CounterViewModel", "Error saving data in onAppClose", e)
            }
        }
    }

    suspend fun saveCurrentJobData() {
        _selectedJob.value?.let { job ->
            Log.d("CounterViewModel", "Saving data for job ${job.id}")
            
            // Get current pastures from memory
            val currentPastures = _jobPastures.value[job.id] ?: emptyList()
            Log.d("CounterViewModel", "Saving ${currentPastures.size} pastures")
            
            // Save pastures
            _jobPastures.update { currentMap ->
                currentMap + (job.id to currentPastures)
            }
            dataStore.saveJobPastures(_jobPastures.value)
            Log.d("CounterViewModel", "Successfully saved pastures")
            
            // Save history
            Log.d("CounterViewModel", "Saving history - size: ${_history.value.size}")
            dataStore.saveJobHistory(job.id, _history.value)
            Log.d("CounterViewModel", "Successfully saved history")
            
            // Save survey state
            Log.d("CounterViewModel", "Saving survey state - isStarted: ${_isSurveyStarted.value}, isExported: ${_isExported.value}")
            dataStore.saveJobSurveyState(job.id, _isSurveyStarted.value, _isExported.value)
            Log.d("CounterViewModel", "Successfully saved survey state")
            
            // Save survey response if exists
            _surveyResponse.value?.let { response ->
                dataStore.saveJobSurveyResponse(job.id, response)
                Log.d("CounterViewModel", "Successfully saved survey response")
            }
        } ?: Log.d("CounterViewModel", "No selected job to save data for")
    }

    fun isChecklistCompleted(jobId: String?, checklistType: String): Boolean {
        if (jobId == null) return false
        // First check the job's own checklist completion
        val selectedJob = _selectedJob.value
        if (selectedJob?.id == jobId && selectedJob.checklistCompletion.contains(checklistType)) {
            return true
        }
        // Fall back to the data store completion map
        return _checklistCompletion.value[jobId]?.contains(checklistType) == true
    }

    fun markChecklistCompletedForCurrentJob(checklistType: String) {
        val jobId = _selectedJob.value?.id ?: return
        viewModelScope.launch {
            // Update the job's checklist completion
            _selectedJob.update { job ->
                job?.copy(checklistCompletion = job.checklistCompletion + checklistType)
            }
            
            // Update active jobs list
            _activeJobs.update { jobs ->
                jobs.map { job ->
                    if (job.id == jobId) {
                        job.copy(checklistCompletion = job.checklistCompletion + checklistType)
                    } else {
                        job
                    }
                }
            }
            
            // Save to data store
            dataStore.markChecklistCompleted(jobId, checklistType)
            dataStore.saveActiveJobs(_activeJobs.value)
            
            Log.d("CounterViewModel", "Marked checklist $checklistType as completed for job $jobId")
            // Only set isSurveyStarted to true if pre_flight checklist is completed
            if (checklistType == "pre_flight") {
                startSurvey()
            }
        }
    }

    // Get the next required checklist for a job
    fun getNextRequiredChecklist(jobId: String?): String? {
        if (jobId == null) return null
        
        val checklists = listOf("pre_drive", "pre_flight", "equipment_setup", "post_flight", "post_job")
        
        for (checklist in checklists) {
            if (!isChecklistCompleted(jobId, checklist)) {
                return checklist
            }
        }
        
        return null // All checklists completed
    }

    // Check if a job is ready to start survey (pre-flight checklist completed)
    fun isJobReadyForSurvey(jobId: String?): Boolean {
        if (jobId == null) return false
        return isChecklistCompleted(jobId, "pre_drive") && 
               isChecklistCompleted(jobId, "pre_flight")
    }

    // Check if a job is ready to finish (all checklists except post-job completed)
    fun isJobReadyToFinish(jobId: String?): Boolean {
        if (jobId == null) return false
        return isChecklistCompleted(jobId, "pre_drive") && 
               isChecklistCompleted(jobId, "pre_flight") &&
               isChecklistCompleted(jobId, "equipment_setup") &&
               isChecklistCompleted(jobId, "post_flight")
    }

    fun setShowPostFlightSurvey(show: Boolean) {
        val jobId = _selectedJob.value?.id ?: return
        _showPostFlightSurvey.value = show
        viewModelScope.launch {
            dataStore.saveShowPostFlightSurvey(jobId, show)
        }
    }
} 