package com.example.finalapp.util

import android.util.Log
import com.example.finalapp.util.Constants.ErrorMessages
import kotlinx.coroutines.CancellationException
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * Centralized error handling utility
 */
object ErrorHandler {

    /**
     * Convert exceptions to user-friendly error messages
     */
    fun getErrorMessage(throwable: Throwable): String {
        Log.e("ErrorHandler", "Error occurred", throwable)

        return when (throwable) {
            is CancellationException -> {
                // Don't log cancellation exceptions as errors
                "Operation was cancelled"
            }
            is UnknownHostException, is SocketTimeoutException -> {
                ErrorMessages.NETWORK_ERROR
            }
            is IOException -> {
                ErrorMessages.NETWORK_ERROR
            }
            is NumberFormatException -> {
                "Invalid number format. Please check your input."
            }
            is IllegalArgumentException -> {
                throwable.message ?: ErrorMessages.VALIDATION_ERROR
            }
            is IllegalStateException -> {
                throwable.message ?: "Invalid operation. Please try again."
            }
            is SecurityException -> {
                "Permission denied. Please check app permissions."
            }
            is OutOfMemoryError -> {
                "Not enough memory. Please close other apps and try again."
            }
            else -> {
                // For unknown exceptions, provide a generic message but log the details
                Log.e("ErrorHandler", "Unknown error: ${throwable.javaClass.simpleName}", throwable)
                throwable.message?.takeIf { it.isNotBlank() } ?: ErrorMessages.GENERIC_ERROR
            }
        }
    }

    /**
     * Handle Result failures with consistent error messaging
     */
    inline fun <T> handleResult(
        result: Result<T>,
        onSuccess: (T) -> Unit,
        onError: (String) -> Unit
    ) {
        result.fold(
            onSuccess = onSuccess,
            onFailure = { throwable ->
                onError(getErrorMessage(throwable))
            }
        )
    }

    /**
     * Execute a suspending operation with error handling
     */
    suspend inline fun <T> safeCall(
        operation: suspend () -> T,
        onError: (String) -> Unit = {}
    ): T? {
        return try {
            operation()
        } catch (e: CancellationException) {
            throw e // Re-throw cancellation exceptions
        } catch (e: Exception) {
            onError(getErrorMessage(e))
            null
        }
    }

    /**
     * Execute a non-suspending operation with error handling
     */
    inline fun <T> safeTry(
        operation: () -> T,
        onError: (String) -> Unit = {}
    ): T? {
        return try {
            operation()
        } catch (e: Exception) {
            onError(getErrorMessage(e))
            null
        }
    }

    /**
     * Validate and execute operation with error handling
     */
    inline fun <T> validateAndExecute(
        validation: () -> ValidationUtils.ValidationResult,
        operation: () -> T,
        onValidationError: (String) -> Unit,
        onOperationError: (String) -> Unit
    ): T? {
        val validationResult = validation()
        if (!validationResult.isValid) {
            onValidationError(validationResult.errorMessage ?: ErrorMessages.VALIDATION_ERROR)
            return null
        }

        return safeTry(operation, onOperationError)
    }

    /**
     * Validate and execute operation with error handling (uses same handler for both validation and operation errors)
     */
    inline fun <T> validateAndExecute(
        validation: () -> ValidationUtils.ValidationResult,
        operation: () -> T,
        onError: (String) -> Unit
    ): T? {
        return validateAndExecute(validation, operation, onError, onError)
    }

    /**
     * Log error with context information
     */
    fun logError(
        tag: String,
        message: String,
        throwable: Throwable? = null,
        context: Map<String, Any?> = emptyMap()
    ) {
        val contextString = if (context.isNotEmpty()) {
            context.entries.joinToString(", ") { "${it.key}=${it.value}" }
        } else ""

        val fullMessage = if (contextString.isNotEmpty()) {
            "$message | Context: $contextString"
        } else message

        if (throwable != null) {
            Log.e(tag, fullMessage, throwable)
        } else {
            Log.e(tag, fullMessage)
        }
    }

    /**
     * Create a standardized error result
     */
    fun <T> createErrorResult(message: String, cause: Throwable? = null): Result<T> {
        return Result.failure(Exception(message, cause))
    }

    /**
     * Check if an error is recoverable (user can retry)
     */
    fun isRecoverableError(throwable: Throwable): Boolean {
        return when (throwable) {
            is IOException,
            is SocketTimeoutException,
            is UnknownHostException -> true
            is IllegalStateException -> true
            is CancellationException -> false
            is OutOfMemoryError -> false
            is SecurityException -> false
            else -> true // Most errors are potentially recoverable
        }
    }

    /**
     * Get retry suggestion based on error type
     */
    fun getRetrySuggestion(throwable: Throwable): String? {
        return when (throwable) {
            is UnknownHostException, is SocketTimeoutException ->
                "Please check your internet connection and try again."
            is IOException ->
                "There was a network issue. Please try again."
            is OutOfMemoryError ->
                "Please close other apps to free up memory and try again."
            is SecurityException ->
                "Please check app permissions in your device settings."
            else -> if (isRecoverableError(throwable)) "Please try again." else null
        }
    }

    /**
     * Format error for user display
     */
    fun formatErrorForUser(throwable: Throwable): Pair<String, String?> {
        val message = getErrorMessage(throwable)
        val suggestion = getRetrySuggestion(throwable)
        return message to suggestion
    }

    /**
     * Common error types for the application
     */
    sealed class AppError(message: String, cause: Throwable? = null) : Exception(message, cause) {
        class ValidationError(message: String) : AppError(message)
        class NetworkError(message: String, cause: Throwable? = null) : AppError(message, cause)
        class DataError(message: String, cause: Throwable? = null) : AppError(message, cause)
        class PermissionError(message: String) : AppError(message)
        class ConfigurationError(message: String) : AppError(message)
    }
}