package com.example.finalapp.util

/**
 * Application-wide constants
 */
object Constants {
    
    // Data Store Keys
    object DataStore {
        const val MAX_HISTORY_ITEMS_PER_JOB = 100
        const val MIN_CHANGE_THRESHOLD = 1
    }
    
    // UI Constants
    object UI {
        const val GRID_MIN_SIZE_DP = 160
        const val CARD_PADDING_DP = 2
        const val BUTTON_PADDING_DP = 16
        const val STANDARD_PADDING_DP = 8
        const val SMALL_PADDING_DP = 4
        const val LARGE_PADDING_DP = 16
        const val DROPDOWN_WIDTH_FRACTION = 0.9f
        const val ALPHA_DISABLED = 0.5f
        const val ALPHA_ENABLED = 1f
    }
    
    // Temperature Constants
    object Temperature {
        const val DEFAULT_TEMPERATURE = 0.0
        const val MIN_TEMPERATURE = -50.0
        const val MAX_TEMPERATURE = 120.0
    }
    
    // Counter Constants
    object Counter {
        const val DEFAULT_VALUE = 0
        const val MIN_VALUE = 0
        const val MAX_VALUE = 999999
        const val DEFAULT_INCREMENT = 1
    }
    
    // Date Format Constants
    object DateFormat {
        const val STANDARD_FORMAT = "yyyy-MM-dd HH:mm:ss"
        const val API_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'"
        const val DISPLAY_FORMAT = "MMM dd, yyyy HH:mm"
    }
    
    // Validation Messages
    object ValidationMessages {
        const val DUPLICATE_PASTURE_NAME = "A pasture with this name already exists"
        const val EMPTY_PASTURE_NAME = "Pasture name cannot be empty"
        const val INVALID_TEMPERATURE = "Please enter a valid temperature"
        const val TEMPERATURE_OUT_OF_RANGE = "Temperature must be between ${Temperature.MIN_TEMPERATURE}°F and ${Temperature.MAX_TEMPERATURE}°F"
        const val EMPTY_COUNTER_NAME = "Counter name cannot be empty"
        const val DUPLICATE_COUNTER_NAME = "A counter with this name already exists"
    }
    
    // Dialog Messages
    object DialogMessages {
        const val DELETE_PASTURE_TITLE = "Delete Pasture"
        const val DELETE_PASTURE_MESSAGE = "Are you sure you want to delete \"%s\"? All counters and data for this pasture will be lost."
        const val DELETE_COUNTER_TITLE = "Delete Counter"
        const val DELETE_COUNTER_MESSAGE = "Are you sure you want to delete %s?"
        const val FINISH_SURVEY_TITLE = "Finish Survey"
        const val FINISH_SURVEY_MESSAGE = "Are you sure you want to finish the survey?"
        const val CANCEL_SURVEY_TITLE = "Cancel Survey"
        const val CANCEL_SURVEY_MESSAGE = "Are you sure you want to cancel this survey? All data for this job will be lost."
    }
    
    // Button Labels
    object ButtonLabels {
        const val ADD = "Add"
        const val DELETE = "Delete"
        const val CANCEL = "Cancel"
        const val YES = "Yes"
        const val NO = "No"
        const val CLOSE = "Close"
        const val SAVE = "Save"
        const val FINISH_SURVEY = "Finish Survey"
        const val CANCEL_SURVEY = "Cancel Survey"
        const val KEEP_SURVEY = "Keep Survey"
        const val ADD_PASTURE = "Add Pasture"
        const val ADD_FIRST_PASTURE = "Add First Pasture"
        const val ADD_COUNTER = "Add Counter"
        const val SELECT_PASTURE = "Select Pasture"
        const val ADD_SELECTED = "Add Selected"
    }
    
    // Content Descriptions
    object ContentDescriptions {
        const val ADD_PASTURE = "Add Pasture"
        const val DELETE_PASTURE = "Delete pasture"
        const val DELETE_COUNTER = "Delete Counter"
        const val HISTORY = "History"
        const val CANCEL_SURVEY = "Cancel Survey"
        const val OPEN_DROPDOWN = "Open dropdown"
        const val CLOSE_DROPDOWN = "Close dropdown"
        const val JOB_DETAILS = "Job Details"
        const val VIEW_MAP = "View Map"
        const val VIEW_PDF = "View PDF"
    }
    
    // Field Labels
    object FieldLabels {
        const val PASTURE_NAME = "Pasture Name"
        const val TEMPERATURE_F = "Temperature (°F)"
        const val TEMPERATURE_C = "Temperature (°C)"
        const val COUNTER_NAME = "Counter Name"
        const val CUSTOM_AMOUNT = "Custom Amount"
    }
    
    // Error Messages
    object ErrorMessages {
        const val GENERIC_ERROR = "An error occurred. Please try again."
        const val NETWORK_ERROR = "Network error. Please check your connection."
        const val SAVE_ERROR = "Failed to save data. Please try again."
        const val LOAD_ERROR = "Failed to load data. Please try again."
        const val EXPORT_ERROR = "Failed to export data. Please try again."
        const val VALIDATION_ERROR = "Please check your input and try again."
    }
    
    // Success Messages
    object SuccessMessages {
        const val DATA_SAVED = "Data saved successfully"
        const val EXPORT_SUCCESS = "Data exported successfully"
        const val SURVEY_COMPLETED = "Survey completed successfully"
    }
    
    // Checklist Types
    object ChecklistTypes {
        const val PRE_DRIVE = "pre_drive"
        const val PRE_FLIGHT = "pre_flight"
        const val EQUIPMENT_SETUP = "equipment_setup"
        const val POST_FLIGHT = "post_flight"
        const val POST_JOB = "post_job"
    }
    
    // Job Status
    object JobStatus {
        const val ACTIVE = "Active"
        const val COMPLETED = "Completed"
        const val CANCELLED = "Cancelled"
    }
    
    // Species Categories (commonly used ones)
    object SpeciesCategories {
        val COMMON_CATEGORIES = listOf(
            "Males", "Females", "Calves",
            "Bucks", "Does", "Fawns",
            "Rams", "Ewes", "Lambs",
            "Bulls", "Cows", "Calves",
            "Stallions", "Mares", "Foals"
        )
    }
}
