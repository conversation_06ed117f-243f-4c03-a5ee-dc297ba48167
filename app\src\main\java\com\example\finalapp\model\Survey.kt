package com.example.finalapp.model

import kotlinx.serialization.*
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

@Serializable
data class Buffer(
    val type: String,
    val data: List<Int>
)

object StringOrIntSerializer : KSerializer<String> {
    override val descriptor = PrimitiveSerialDescriptor("StringOrInt", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: String) {
        encoder.encodeString(value)
    }

    override fun deserialize(decoder: Decoder): String {
        return when (val value = decoder.decodeString()) {
            "null" -> ""
            else -> value
        }
    }
}

@Serializable
data class Survey(
    @Serializable(with = StringOrIntSerializer::class)
    val id: String? = null,
    val property_name: String? = null,
    val coordinates: String? = null,
    val gate_code: String? = null,
    val notes: String? = null,
    val driving_instructions: String? = null,
    val scheduled_time: String? = null,
    val image: ImageData? = null,
    val pdf: PdfData? = null,
    val google_sheets_id: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Survey

        if (id != other.id) return false
        if (property_name != other.property_name) return false
        if (coordinates != other.coordinates) return false
        if (gate_code != other.gate_code) return false
        if (notes != other.notes) return false
        if (driving_instructions != other.driving_instructions) return false
        if (scheduled_time != other.scheduled_time) return false
        if (image != other.image) return false
        if (pdf != other.pdf) return false
        if (google_sheets_id != other.google_sheets_id) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id?.hashCode() ?: 0
        result = 31 * result + (property_name?.hashCode() ?: 0)
        result = 31 * result + (coordinates?.hashCode() ?: 0)
        result = 31 * result + (gate_code?.hashCode() ?: 0)
        result = 31 * result + (notes?.hashCode() ?: 0)
        result = 31 * result + (driving_instructions?.hashCode() ?: 0)
        result = 31 * result + (scheduled_time?.hashCode() ?: 0)
        result = 31 * result + (image?.hashCode() ?: 0)
        result = 31 * result + (pdf?.hashCode() ?: 0)
        result = 31 * result + (google_sheets_id?.hashCode() ?: 0)
        return result
    }
}

@Serializable
data class ImageData(
    val data: List<Int>
)

@Serializable
data class PdfData(
    val data: List<Int>
) 